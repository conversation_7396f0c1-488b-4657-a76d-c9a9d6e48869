<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مولد فيديوهات القرآن الكريم</title>
    <link rel="stylesheet" href="style.css">
    <!-- استيراد خطوط عربية جميلة -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Scheherazade+New:wght@400;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- الشريط العلوي -->
    <header class="top-header">
        <h1>🕌 مولد فيديوهات القرآن الكريم</h1>
        <p>أنشئ فيديوهات قرآنية جميلة مع تلاوة صوتية</p>
    </header>

    <!-- التخطيط الرئيسي -->
    <div class="main-layout">
        <!-- الجانب الأيسر: أدوات التحكم -->
        <div class="controls-panel">
            <!-- تبويبات -->
            <div class="tabs">
                <button class="tab-button active" onclick="switchTab('quran', this)">📖 إنشاء من القرآن</button>
                <button class="tab-button" onclick="switchTab('upload', this)">📁 رفع ملفات مخصصة</button>
            </div>

            <!-- وضع إنشاء من القرآن -->
            <div id="quranTab" class="tab-content active">
                <!-- قسم اختيار المحتوى -->
                <div class="control-section">
                    <h3>📖 اختيار المحتوى</h3>
                    <div class="input-group">
                        <label for="chapterSelect">السورة:</label>
                        <select id="chapterSelect" onchange="loadVerses()">
                            <option value="">اختر السورة...</option>
                        </select>
                    </div>

                    <!-- زر الآية العشوائية -->
                    <div class="input-group">
                        <button id="randomVerseBtn" onclick="selectRandomVerse()" class="random-verse-btn">
                            🎲 آية عشوائية طويلة
                        </button>
                        <small class="random-verse-hint">اختر آية طويلة وجميلة بشكل عشوائي</small>
                    </div>

                    <div class="input-group" id="versesSection" style="display:none;">
                        <label for="versesSelect">الآيات:</label>
                        <div id="versesContainer">
                            <p>جاري تحميل الآيات...</p>
                        </div>
                    </div>

                    <div class="input-group">
                        <label for="reciterSelect">القارئ:</label>
                        <select id="reciterSelect">
                            <option value="">اختر القارئ...</option>
                        </select>
                    </div>
                </div>

                <!-- قسم إعدادات الفيديو -->
                <div class="control-section">
                    <h3>🎬 إعدادات الفيديو</h3>
                    <div class="input-group">
                        <label for="videoOrientation">اتجاه الفيديو:</label>
                        <select id="videoOrientation">
                            <option value="landscape">📺 أفقي (للكمبيوتر والتلفزيون) - 1280x720</option>
                            <option value="portrait">📱 عمودي (للموبايل) - 720x1280</option>
                            <option value="square">⬜ مربع (لوسائل التواصل) - 1080x1080</option>
                        </select>
                        <small>اختر الاتجاه المناسب للمنصة التي ستنشر عليها الفيديو</small>
                    </div>

                    <div class="input-group">
                        <label for="customImageInput">صورة خلفية مخصصة (اختيارية):</label>
                        <input type="file" id="customImageInput" accept="image/*">
                        <small>يمكنك رفع صورة خلفية مخصصة بدلاً من الخلفية الافتراضية</small>
                        <div id="imagePreview" style="display: none; margin-top: 10px;">
                            <img id="previewImg" style="max-width: 200px; max-height: 150px; border-radius: 8px; border: 2px solid #ddd;">
                            <p id="imageInfo" style="font-size: 12px; color: #666; margin-top: 5px;"></p>
                            <button type="button" id="removeImageBtn" style="background: #dc3545; padding: 5px 10px; font-size: 12px; margin-top: 5px;">
                                🗑️ إزالة الصورة
                            </button>
                        </div>
                    </div>
                </div>



                <!-- قسم تخصيص النص -->
                <div id="textCustomization" class="control-section" style="display: none;">
                    <h3>🎨 تخصيص النص</h3>
                    <div class="customization-grid">
                        <!-- حجم الخط -->
                        <div class="custom-group">
                            <label for="fontSize">📏 حجم الخط:</label>
                            <input type="range" id="fontSize" min="16" max="80" value="36" step="2">
                            <span id="fontSizeValue">36px</span>
                        </div>

                        <!-- لون النص -->
                        <div class="custom-group">
                            <label for="textColor">🎨 لون النص:</label>
                            <input type="color" id="textColor" value="#ffffff">
                            <button type="button" class="preset-btn" onclick="setTextColor('#ffffff')">أبيض</button>
                            <button type="button" class="preset-btn" onclick="setTextColor('#ffd700')">ذهبي</button>
                            <button type="button" class="preset-btn" onclick="setTextColor('#000000')">أسود</button>
                        </div>

                        <!-- نمط الخط -->
                        <div class="custom-group">
                            <label for="fontWeight">💪 نمط الخط:</label>
                            <select id="fontWeight">
                                <option value="normal">عادي</option>
                                <option value="bold" selected>عريض</option>
                                <option value="bolder">عريض جداً</option>
                            </select>
                        </div>

                        <!-- نوع الخط -->
                        <div class="custom-group">
                            <label for="fontFamily">🔤 نوع الخط:</label>
                            <select id="fontFamily">
                                <option value="Amiri" selected>أميري (Amiri)</option>
                                <option value="Uthmanic">عثماني (Uthmanic)</option>
                                <option value="Arial">أريال (Arial)</option>
                                <option value="Tahoma">تاهوما (Tahoma)</option>
                            </select>
                        </div>

                        <!-- موقع النص -->
                        <div class="custom-group">
                            <label for="textPosition">📍 موقع النص:</label>
                            <select id="textPosition">
                                <option value="bottom" selected>أسفل</option>
                                <option value="top">أعلى</option>
                                <option value="center">وسط</option>
                                <option value="bottom-center">أسفل الوسط</option>
                            </select>
                        </div>

                        <!-- خلفية النص -->
                        <div class="custom-group">
                            <label for="textBgOpacity">🌫️ شفافية خلفية النص:</label>
                            <input type="range" id="textBgOpacity" min="0" max="100" value="70" step="5">
                            <span id="textBgOpacityValue">70%</span>
                        </div>

                        <!-- لون خلفية النص -->
                        <div class="custom-group">
                            <label for="textBgColor">🎭 لون خلفية النص:</label>
                            <input type="color" id="textBgColor" value="#000000">
                            <button type="button" class="preset-btn" onclick="setTextBgColor('#000000')">أسود</button>
                            <button type="button" class="preset-btn" onclick="setTextBgColor('#1e3c72')">أزرق</button>
                            <button type="button" class="preset-btn" onclick="setTextBgColor('#2d5016')">أخضر</button>
                        </div>

                        <!-- ظل النص -->
                        <div class="custom-group">
                            <label for="textShadow">✨ قوة ظل النص:</label>
                            <input type="range" id="textShadow" min="0" max="10" value="6" step="1">
                            <span id="textShadowValue">6px</span>
                        </div>

                        <!-- المسافة من الحواف -->
                        <div class="custom-group">
                            <label for="textPadding">📐 المسافة من الحواف:</label>
                            <input type="range" id="textPadding" min="20" max="100" value="40" step="10">
                            <span id="textPaddingValue">40px</span>
                        </div>

                        <!-- أزرار الإعدادات المسبقة -->
                        <div class="custom-group presets-group">
                            <label>🎯 إعدادات مسبقة:</label>
                            <div class="presets-buttons">
                                <button type="button" class="preset-style-btn" onclick="applyPreset('classic')">🏛️ كلاسيكي</button>
                                <button type="button" class="preset-style-btn" onclick="applyPreset('modern')">🌟 عصري</button>
                                <button type="button" class="preset-style-btn" onclick="applyPreset('elegant')">💎 أنيق</button>
                                <button type="button" class="preset-style-btn" onclick="applyPreset('bold')">🔥 جريء</button>
                            </div>
                        </div>

                        <!-- زر إعادة التعيين -->
                        <div class="custom-group">
                            <button type="button" class="reset-btn" onclick="resetTextSettings()">🔄 إعادة تعيين</button>
                        </div>
                    </div>
                </div>

                <!-- زر إنشاء الفيديو -->
                <div class="control-section">
                    <button id="createQuranVideoBtn" onclick="createQuranVideo()" disabled>
                        🎬 توليد الفيديو
                    </button>
                </div>
            </div>
        </div>

        <!-- الجانب الأيمن: المعاينة المباشرة -->
        <div class="preview-panel">
            <!-- معاينة الآيات المختارة -->
            <div id="versesPreview" class="preview-section" style="display:none;">
                <h3>📖 الآيات المختارة</h3>
                <div id="selectedVersesText" class="verses-text"></div>
            </div>

            <!-- المعاينة المباشرة للنتيجة النهائية -->
            <div id="livePreview" class="preview-section" style="display: none;">
                <h3>🔍 معاينة النتيجة النهائية</h3>
                <div id="previewContainer">
                    <canvas id="previewCanvas"></canvas>
                    <div id="previewInfo"></div>
                </div>
            </div>

            <!-- منطقة النتائج والتحميل -->
            <div id="resultsSection" class="preview-section" style="display:none;">
                <h3>🎬 النتيجة النهائية</h3>
                <div id="loadingMessage" style="display:none;">
                    <div class="loading-container">
                        <div class="loading-icon">🎬</div>
                        <h4>جاري إنشاء الفيديو القرآني</h4>
                        <div class="progress-container">
                            <div class="progress-bar">
                                <div class="progress-fill" id="progressFill"></div>
                            </div>
                            <div class="progress-text" id="progressText">0%</div>
                        </div>
                        <div class="loading-steps">
                            <div class="step" id="step1">📥 تنزيل الملفات الصوتية...</div>
                            <div class="step" id="step2">🖼️ إنشاء الصورة...</div>
                            <div class="step" id="step3">🎵 معالجة الصوت...</div>
                            <div class="step" id="step4">🎬 إنشاء الفيديو...</div>
                        </div>
                        <p class="loading-subtitle">يرجى الانتظار، قد تستغرق العملية بضع دقائق</p>
                    </div>
                </div>
                <video id="videoPreview" controls style="display:none; width: 100%; max-width: 500px; border-radius: 8px; box-shadow: 0 4px 8px rgba(0,0,0,0.1);"></video>
                <a id="downloadLink" style="display:none;" class="download-btn">📥 تحميل الفيديو</a>
            </div>
            <!-- أزرار تشخيص مؤقتة -->
            <div class="control-section debug-section">
                <h3>🔧 أدوات التشخيص</h3>
                <div class="debug-buttons">
                    <button onclick="debugStatus()" class="debug-btn">🔍 تشخيص</button>
                    <button onclick="selectFirstVerse()" class="debug-btn">✅ آية</button>
                    <button onclick="testVideoCreation()" class="debug-btn">🎬 فيديو</button>
                    <button onclick="testAudioDownload()" class="debug-btn">🔊 صوت</button>
                    <button onclick="testPopularReciters()" class="debug-btn">👥 قراء</button>
                    <button onclick="testDirectUrls()" class="debug-btn">🔗 روابط</button>
                </div>
            </div>

            <!-- وضع رفع الملفات المخصصة -->
            <div id="uploadTab" class="tab-content">
                <div class="control-section">
                    <h3>📁 رفع ملفات مخصصة</h3>
                    <div class="input-group">
                        <label for="imageInput">الصورة:</label>
                        <input type="file" id="imageInput" accept="image/jpeg, image/png">
                    </div>

                    <div class="input-group">
                        <label for="audioInput">الملف الصوتي:</label>
                        <input type="file" id="audioInput" accept="audio/mp3, audio/wav">
                    </div>

                    <button onclick="createVideo()">إنشاء الفيديو</button>
                </div>
            </div>
        </div>
    </div>

    <!-- رسائل الخطأ -->
    <div id="errorMessage" class="error-message" style="display:none;"></div>

    <script src="script.js"></script>
</body>
</html>
