// متغيرات عامة
let chapters = [];
let verses = [];
let recitations = [];
let selectedVerses = [];

// تحميل البيانات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    loadChapters();
    loadRecitations();
});

// تبديل التبويبات
function switchTab(tabName, buttonElement) {
    // إخفاء جميع التبويبات
    document.querySelectorAll('.tab-content').forEach(tab => {
        tab.classList.remove('active');
    });

    // إزالة الفئة النشطة من جميع الأزرار
    document.querySelectorAll('.tab-button').forEach(btn => {
        btn.classList.remove('active');
    });

    // إظهار التبويب المحدد
    document.getElementById(tabName + 'Tab').classList.add('active');

    // تفعيل الزر المحدد
    if (buttonElement) {
        buttonElement.classList.add('active');
    } else {
        // البحث عن الزر بناءً على النص
        document.querySelectorAll('.tab-button').forEach(btn => {
            if (btn.onclick && btn.onclick.toString().includes(tabName)) {
                btn.classList.add('active');
            }
        });
    }
}

// تحميل قائمة السور
async function loadChapters() {
    try {
        const response = await fetch('http://localhost:5000/api/chapters');
        if (!response.ok) throw new Error('فشل في تحميل السور');

        chapters = await response.json();
        const chapterSelect = document.getElementById('chapterSelect');

        chapterSelect.innerHTML = '<option value="">اختر السورة...</option>';
        chapters.forEach(chapter => {
            const option = document.createElement('option');
            option.value = chapter.id;
            option.textContent = `${chapter.id}. ${chapter.name_arabic}`;
            chapterSelect.appendChild(option);
        });
    } catch (error) {
        showError('خطأ في تحميل السور: ' + error.message);
    }
}

// تحميل قائمة القراء
async function loadRecitations() {
    try {
        const response = await fetch('http://localhost:5000/api/recitations');
        if (!response.ok) throw new Error('فشل في تحميل القراء');

        recitations = await response.json();
        const reciterSelect = document.getElementById('reciterSelect');

        reciterSelect.innerHTML = '<option value="">اختر القارئ...</option>';
        recitations.forEach(recitation => {
            const option = document.createElement('option');
            option.value = recitation.id;
            option.textContent = recitation.reciter_name;
            reciterSelect.appendChild(option);
        });

        // إضافة event listener للقارئ بعد تحميل القائمة
        reciterSelect.addEventListener('change', function() {
            console.log('تم تغيير القارئ:', this.value);
            updateCreateButton();
        });

        console.log('تم تحميل', recitations.length, 'قارئ');
        updateCreateButton(); // تحديث الزر بعد تحميل القراء

    } catch (error) {
        showError('خطأ في تحميل القراء: ' + error.message);
    }
}

// تحميل آيات السورة المختارة
async function loadVerses() {
    const chapterSelect = document.getElementById('chapterSelect');
    const chapterId = chapterSelect.value;

    if (!chapterId) {
        document.getElementById('versesSection').style.display = 'none';
        return;
    }

    try {
        const response = await fetch(`http://localhost:5000/api/verses/${chapterId}`);
        if (!response.ok) throw new Error('فشل في تحميل الآيات');

        verses = await response.json();

        // طباعة بنية البيانات للتشخيص
        console.log('📊 بنية بيانات الآيات:', {
            totalVerses: verses.length,
            firstVerse: verses[0],
            verseStructure: verses[0] ? Object.keys(verses[0]) : 'لا توجد آيات'
        });

        // طباعة تفاصيل أكثر للتشخيص
        if (verses.length > 0) {
            console.log('🔍 تفاصيل الآية الأولى:');
            console.log('verse_number:', verses[0].verse_number);
            console.log('verse_key:', verses[0].verse_key);
            console.log('text_uthmani:', verses[0].text_uthmani);
            console.log('جميع المفاتيح:', Object.keys(verses[0]));
            console.log('جميع القيم:', Object.values(verses[0]));
        }

        displayVerses();
        document.getElementById('versesSection').style.display = 'block';
    } catch (error) {
        showError('خطأ في تحميل الآيات: ' + error.message);
    }
}

// عرض الآيات مع خانات الاختيار
function displayVerses() {
    console.log('🔄 عرض الآيات...', verses.length, 'آية');

    const versesContainer = document.getElementById('versesContainer');
    versesContainer.innerHTML = '';

    verses.forEach((verse, index) => {
        // تحديد رقم الآية من البيانات المتاحة
        const verseNumber = verse.verse_number || verse.verse_key || (index + 1);
        const verseText = verse.text_uthmani || verse.text || 'نص غير متوفر';

        console.log(`إنشاء آية ${index + 1}:`, {
            originalData: verse,
            extractedNumber: verseNumber,
            extractedText: verseText.substring(0, 30) + '...'
        });

        const verseDiv = document.createElement('div');
        verseDiv.className = 'verse-item';

        const checkbox = document.createElement('input');
        checkbox.type = 'checkbox';
        checkbox.id = `verse_${verseNumber}`;
        checkbox.dataset.verseIndex = index; // إضافة index كـ backup

        // استخدام addEventListener بدلاً من onchange
        checkbox.addEventListener('change', function() {
            console.log('تم تغيير حالة الآية:', verseNumber, 'إلى:', this.checked);
            updateSelectedVerses();
        });

        const label = document.createElement('label');
        label.htmlFor = `verse_${verseNumber}`;
        label.className = 'verse-text';
        label.textContent = `(${verseNumber}) ${verseText}`;

        // إضافة click listener للـ label أيضاً
        label.addEventListener('click', function() {
            checkbox.checked = !checkbox.checked;
            console.log('تم النقر على label للآية:', verseNumber, 'الحالة الجديدة:', checkbox.checked);
            updateSelectedVerses();
        });

        verseDiv.appendChild(checkbox);
        verseDiv.appendChild(label);
        versesContainer.appendChild(verseDiv);
    });

    console.log('✅ تم عرض جميع الآيات');

    // إعادة تعيين selectedVerses
    selectedVerses = [];
    updateCreateButton();

    // تحديث المعاينة المباشرة
    updateLivePreview();
}

// تحديث الآيات المختارة
function updateSelectedVerses() {
    console.log('🔄 تحديث الآيات المختارة...');

    selectedVerses = [];
    const checkboxes = document.querySelectorAll('#versesContainer input[type="checkbox"]:checked');
    const chapterSelect = document.getElementById('chapterSelect');
    const currentChapterId = chapterSelect ? chapterSelect.value : null;

    console.log('عدد checkboxes المحددة:', checkboxes.length);
    console.log('عدد الآيات المتاحة:', verses.length);
    console.log('السورة الحالية:', currentChapterId);

    checkboxes.forEach((checkbox, index) => {
        console.log(`Checkbox ${index + 1}:`, checkbox.id);

        // استخراج رقم الآية من ID
        const verseNumberFromId = checkbox.id.split('_')[1];
        console.log('رقم الآية من ID:', verseNumberFromId);

        // استخدام index كـ backup إذا فشل استخراج الرقم
        const verseIndex = parseInt(checkbox.dataset.verseIndex);
        console.log('فهرس الآية:', verseIndex);

        // البحث عن الآية بطرق متعددة
        let verse = null;

        // الطريقة الأولى: البحث بـ verse_number
        if (verseNumberFromId && verseNumberFromId !== 'undefined') {
            const verseNumber = parseInt(verseNumberFromId);
            if (!isNaN(verseNumber)) {
                verse = verses.find(v => (v.verse_number || v.verse_key) == verseNumber);
            }
        }

        // الطريقة الثانية: استخدام الفهرس
        if (!verse && !isNaN(verseIndex) && verses[verseIndex]) {
            verse = verses[verseIndex];
            console.log('تم العثور على الآية باستخدام الفهرس:', verseIndex);
        }

        if (verse) {
            // إضافة chapter_number إذا لم يكن موجوداً
            // استخراج رقم الآية من verse_key إذا لم يكن موجوداً
            let verseNumber = verse.verse_number;
            if (!verseNumber && verse.verse_key) {
                const [chapterNum, verseNum] = verse.verse_key.split(':');
                verseNumber = parseInt(verseNum);
            }
            if (!verseNumber) {
                verseNumber = verseIndex + 1;
            }

            const verseWithChapter = {
                ...verse,
                chapter_number: verse.chapter_number || parseInt(currentChapterId),
                verse_number: verseNumber
            };

            selectedVerses.push(verseWithChapter);
            console.log('تم إضافة الآية:', verseWithChapter.verse_number, 'من السورة:', verseWithChapter.chapter_number);
            console.log('نص الآية:', (verse.text_uthmani || verse.text || 'لا يوجد نص').substring(0, 30) + '...');
        } else {
            console.error('لم يتم العثور على الآية. ID:', checkbox.id, 'Index:', verseIndex);
            console.log('الآيات المتاحة:', verses.map((v, i) => ({index: i, verse_number: v.verse_number, verse_key: v.verse_key})));
        }
    });

    console.log('✅ إجمالي الآيات المختارة:', selectedVerses.length);
    console.log('تفاصيل الآيات المختارة:', selectedVerses);

    updateVersesPreview();
    updateLivePreview(); // تحديث المعاينة المباشرة

    // إظهار أدوات التخصيص عند وجود آيات
    if (selectedVerses.length > 0) {
        showTextCustomization();
    } else {
        hideTextCustomization();
    }

    updateCreateButton();
}

// تحديث معاينة الآيات
function updateVersesPreview() {
    const previewDiv = document.getElementById('versesPreview');
    const textDiv = document.getElementById('selectedVersesText');

    if (selectedVerses.length > 0) {
        // إنشاء نص موحد من الآيات المختارة مع أرقامها داخل الفاصل
        const versesWithNumbers = selectedVerses.map((verse, index) => {
            // تنظيف النص من أي فواصل موجودة مسبقاً
            const cleanText = verse.text_uthmani.replace(/\s*۝\s*/g, '').trim();

            // إضافة رقم الآية الحالية مع فاصل واحد فقط
            const currentVerseNumber = convertToArabicNumbers(verse.verse_number);
            return cleanText + ' ۝' + currentVerseNumber;
        });

        const versesText = versesWithNumbers.join(' ');
        textDiv.textContent = versesText;
        previewDiv.style.display = 'block';

        console.log('📖 تم تحديث معاينة الآيات مع أرقامها داخل الفاصل');
    } else {
        previewDiv.style.display = 'none';
    }
}

// تحديث حالة زر الإنشاء
function updateCreateButton() {
    const createBtn = document.getElementById('createQuranVideoBtn');
    const reciterSelect = document.getElementById('reciterSelect');

    if (!createBtn) {
        console.error('زر الإنشاء غير موجود!');
        return;
    }

    if (!reciterSelect) {
        console.error('قائمة القراء غير موجودة!');
        return;
    }

    const hasVerses = selectedVerses.length > 0;
    const hasReciter = reciterSelect.value !== '';

    console.log('تحديث زر الإنشاء:', {
        hasVerses: hasVerses,
        versesCount: selectedVerses.length,
        hasReciter: hasReciter,
        reciterValue: reciterSelect.value
    });

    const canCreate = hasVerses && hasReciter;
    createBtn.disabled = !canCreate;

    // تغيير نص الزر حسب الحالة
    if (!hasVerses) {
        createBtn.textContent = '🎬 اختر آيات أولاً';
    } else if (!hasReciter) {
        createBtn.textContent = '🎬 اختر قارئ أولاً';
    } else {
        createBtn.textContent = '🎬 توليد الفيديو';
    }
}

// إنشاء فيديو قرآني
async function createQuranVideo() {
    console.log('🎬 بدء عملية إنشاء الفيديو...');

    const reciterSelect = document.getElementById('reciterSelect');
    const customImageInput = document.getElementById('customImageInput');

    // تشخيص مفصل للحالة
    console.log('🔍 تشخيص الحالة:');
    console.log('- عدد الآيات المختارة:', selectedVerses.length);
    console.log('- القارئ المختار:', reciterSelect ? reciterSelect.value : 'غير موجود');
    console.log('- تفاصيل الآيات:', selectedVerses);

    if (selectedVerses.length === 0) {
        console.error('❌ لا توجد آيات مختارة');
        showError('يرجى اختيار آية واحدة على الأقل');
        return;
    }

    if (!reciterSelect || !reciterSelect.value) {
        console.error('❌ لم يتم اختيار قارئ');
        showError('يرجى اختيار قارئ');
        return;
    }

    console.log('✅ جميع الشروط مستوفاة، بدء إنشاء الفيديو...');
    showLoading();

    try {
        // الحصول على اتجاه الفيديو المختار
        const videoOrientation = document.getElementById('videoOrientation').value || 'landscape';

        const requestData = {
            recitation_id: reciterSelect.value,
            verses: selectedVerses,
            custom_image: customImagePath, // استخدام الصورة المخصصة إذا تم رفعها
            video_orientation: videoOrientation, // إضافة اتجاه الفيديو
            text_settings: textSettings // إضافة إعدادات النص المخصصة
        };

        console.log('🚀 إرسال طلب إنشاء الفيديو:', requestData);
        console.log('📡 URL:', 'http://localhost:5000/create_quran_video');
        console.log('📊 تفاصيل الآيات المرسلة:', JSON.stringify(requestData.verses, null, 2));
        console.log('📊 حجم البيانات المرسلة:', JSON.stringify(requestData).length, 'حرف');

        // اختبار الاتصال بالخادم أولاً
        try {
            const testResponse = await fetch('http://localhost:5000/', {method: 'GET'});
            console.log('🔗 اختبار الاتصال بالخادم:', testResponse.ok ? 'نجح' : 'فشل');
        } catch (testError) {
            console.error('❌ فشل الاتصال بالخادم:', testError);
            throw new Error('لا يمكن الاتصال بالخادم. تأكد من تشغيل الخادم على http://localhost:5000');
        }

        console.log('📤 إرسال الطلب...');
        const response = await fetch('http://localhost:5000/create_quran_video', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestData)
        });

        console.log('📨 استجابة الخادم:', response.status, response.statusText);
        console.log('📨 headers الاستجابة:', Object.fromEntries(response.headers.entries()));

        if (!response.ok) {
            console.log('❌ خطأ في الاستجابة:', response.status, response.statusText);

            // قراءة response كـ text أولاً
            const responseText = await response.text();
            console.log('📄 نص الاستجابة:', responseText);

            // محاولة تحويله لـ JSON
            try {
                const errorData = JSON.parse(responseText);
                console.log('📄 تفاصيل الخطأ:', errorData);
                throw new Error(errorData.error || 'فشل في إنشاء الفيديو');
            } catch (jsonError) {
                console.log('❌ الاستجابة ليست JSON صالح');
                throw new Error(`خطأ ${response.status}: ${responseText || response.statusText}`);
            }
        }

        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);

        showVideoResult(url);

    } catch (error) {
        showError('خطأ في إنشاء الفيديو: ' + error.message);
    } finally {
        hideLoading();
    }
}

// إنشاء فيديو من ملفات مرفوعة (الوظيفة الأصلية)
async function createVideo() {
    const imageInput = document.getElementById('imageInput');
    const audioInput = document.getElementById('audioInput');

    const imageFile = imageInput.files[0];
    const audioFile = audioInput.files[0];

    if (!imageFile || !audioFile) {
        showError('يرجى اختيار صورة وملف صوتي');
        return;
    }

    showLoading();

    const formData = new FormData();
    formData.append('image', imageFile);
    formData.append('audio', audioFile);

    try {
        const response = await fetch('http://localhost:5000/create_video', {
            method: 'POST',
            body: formData
        });

        if (!response.ok) {
            throw new Error('فشل في إنشاء الفيديو');
        }

        const blob = await response.blob();
        console.log('📦 حجم الفيديو المستلم:', blob.size, 'bytes');
        console.log('📦 نوع الفيديو:', blob.type);

        const url = window.URL.createObjectURL(blob);
        console.log('🔗 URL الفيديو:', url);

        showVideoResult(url);

    } catch (error) {
        showError('خطأ في إنشاء الفيديو: ' + error.message);
    } finally {
        hideLoading();
    }
}

// عرض نتيجة الفيديو
function showVideoResult(videoUrl) {
    console.log('🎬 عرض نتيجة الفيديو:', videoUrl);

    const resultsSection = document.getElementById('resultsSection');
    const videoPreview = document.getElementById('videoPreview');
    const downloadLink = document.getElementById('downloadLink');

    console.log('🔍 العناصر الموجودة:');
    console.log('- resultsSection:', resultsSection ? 'موجود' : 'غير موجود');
    console.log('- videoPreview:', videoPreview ? 'موجود' : 'غير موجود');
    console.log('- downloadLink:', downloadLink ? 'موجود' : 'غير موجود');

    if (!resultsSection || !videoPreview || !downloadLink) {
        console.error('❌ بعض العناصر غير موجودة!');
        alert('❌ خطأ في العثور على عناصر عرض الفيديو. تحقق من HTML.');
        return;
    }

    try {
        // إعداد الفيديو
        videoPreview.src = videoUrl;
        videoPreview.style.display = 'block';
        videoPreview.controls = true;
        videoPreview.style.width = '100%';
        videoPreview.style.maxWidth = '500px';
        videoPreview.style.borderRadius = '8px';
        videoPreview.style.boxShadow = '0 4px 8px rgba(0,0,0,0.1)';

        // إعداد رابط التحميل
        downloadLink.href = videoUrl;
        downloadLink.download = 'quran_video_' + new Date().getTime() + '.mp4';
        downloadLink.style.display = 'inline-block';
        downloadLink.textContent = '📥 تحميل الفيديو';
        downloadLink.style.marginTop = '10px';

        // إظهار قسم النتائج
        resultsSection.style.display = 'block';

        // إخفاء رسائل الخطأ والتحميل
        hideError();
        hideLoading();

        console.log('✅ تم إعداد عرض الفيديو بنجاح');

        // إخفاء المعاينة المباشرة لإفساح المجال للفيديو
        const livePreview = document.getElementById('livePreview');
        if (livePreview) {
            livePreview.style.display = 'none';
        }

        // التمرير إلى النتائج
        setTimeout(() => {
            resultsSection.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }, 100);

        // إظهار رسالة نجاح
        alert('🎉 تم إنشاء الفيديو بنجاح! يمكنك مشاهدته وتحميله في الجانب الأيمن.');

    } catch (error) {
        console.error('❌ خطأ في إعداد عرض الفيديو:', error);
        alert('❌ خطأ في عرض الفيديو: ' + error.message);
    }
}

// إظهار رسالة التحميل
function showLoading() {
    document.getElementById('loadingMessage').style.display = 'block';
    document.getElementById('resultsSection').style.display = 'block';
    document.getElementById('videoPreview').style.display = 'none';
    document.getElementById('downloadLink').style.display = 'none';

    // إعادة تعيين شريط التقدم
    updateProgress(0, 0);

    // بدء محاكاة التقدم
    simulateProgress();
}

// إخفاء رسالة التحميل
function hideLoading() {
    document.getElementById('loadingMessage').style.display = 'none';
}

// إظهار رسالة خطأ
function showError(message) {
    const errorDiv = document.getElementById('errorMessage');
    errorDiv.textContent = message;
    errorDiv.style.display = 'block';

    // إخفاء الرسالة بعد 5 ثواني
    setTimeout(() => {
        hideError();
    }, 5000);
}

// إخفاء رسالة الخطأ
function hideError() {
    document.getElementById('errorMessage').style.display = 'none';
}

// وظيفة تشخيص الحالة
function debugStatus() {
    const chapterSelect = document.getElementById('chapterSelect');
    const reciterSelect = document.getElementById('reciterSelect');
    const createBtn = document.getElementById('createQuranVideoBtn');
    const versesContainer = document.getElementById('versesContainer');
    const checkboxes = document.querySelectorAll('#versesContainer input[type="checkbox"]:checked');

    const status = {
        'السورة المختارة': chapterSelect ? chapterSelect.value : 'غير موجود',
        'القارئ المختار': reciterSelect ? reciterSelect.value : 'غير موجود',
        'عدد الآيات المتاحة': verses.length,
        'عدد الآيات المختارة': selectedVerses.length,
        'عدد checkboxes المحددة': checkboxes.length,
        'حالة الزر': createBtn ? (createBtn.disabled ? 'معطل' : 'مفعل') : 'غير موجود',
        'نص الزر': createBtn ? createBtn.textContent : 'غير موجود',
        'عدد السور المحملة': chapters.length,
        'عدد القراء المحملين': recitations.length
    };

    console.log('🔍 حالة التطبيق:', status);

    // عرض النتائج في alert
    let message = '🔍 تشخيص الحالة:\n\n';
    for (const [key, value] of Object.entries(status)) {
        message += `${key}: ${value}\n`;
    }

    if (selectedVerses.length > 0) {
        message += '\nالآيات المختارة:\n';
        selectedVerses.forEach((verse, index) => {
            message += `${index + 1}. آية ${verse.verse_number}\n`;
        });
    }

    alert(message);

    // محاولة إصلاح المشكلة
    if (createBtn && createBtn.disabled && checkboxes.length > 0) {
        console.log('🔧 محاولة إصلاح المشكلة...');

        // إعادة تشغيل updateSelectedVerses يدوياً
        updateSelectedVerses();

        if (selectedVerses.length > 0 && reciterSelect && reciterSelect.value) {
            createBtn.disabled = false;
            createBtn.textContent = '🎬 توليد الفيديو';
            alert('تم إصلاح الزر! جرب الآن.');
        } else {
            alert('المشكلة: checkboxes محددة ولكن selectedVerses فارغة. راجع Console للتفاصيل.');
        }
    } else if (checkboxes.length === 0) {
        alert('لم يتم تحديد أي آيات. تأكد من النقر على checkboxes.');
    }
}

// وظيفة اختبار لتحديد الآيات تلقائياً
function selectFirstVerse() {
    console.log('🧪 اختبار تحديد الآية الأولى...');

    const firstCheckbox = document.querySelector('#versesContainer input[type="checkbox"]');
    if (firstCheckbox) {
        firstCheckbox.checked = true;
        console.log('تم تحديد الآية الأولى تلقائياً:', firstCheckbox.id);

        // طباعة معلومات مفصلة
        console.log('معلومات الآية الأولى من verses:', verses[0]);

        updateSelectedVerses();
        alert('تم تحديد الآية الأولى تلقائياً. راجع Console للتفاصيل.');
    } else {
        alert('لا توجد آيات متاحة. اختر سورة أولاً.');
    }
}

// وظيفة تشخيص بنية البيانات
function debugDataStructure() {
    console.log('🔍 تشخيص بنية البيانات:');
    console.log('chapters:', chapters.length > 0 ? chapters[0] : 'فارغة');
    console.log('verses:', verses.length > 0 ? verses[0] : 'فارغة');
    console.log('recitations:', recitations.length > 0 ? recitations[0] : 'فارغة');
    console.log('selectedVerses:', selectedVerses);

    if (verses.length > 0) {
        console.log('مفاتيح الآية الأولى:', Object.keys(verses[0]));
        console.log('قيم الآية الأولى:', verses[0]);
    }

    const checkboxes = document.querySelectorAll('#versesContainer input[type="checkbox"]');
    console.log('عدد checkboxes الموجودة:', checkboxes.length);

    if (checkboxes.length > 0) {
        console.log('أول checkbox:', checkboxes[0].id, 'محدد:', checkboxes[0].checked);
    }
}

// وظيفة اختبار تنزيل الصوت
async function testAudioDownload() {
    const reciterSelect = document.getElementById('reciterSelect');
    const chapterSelect = document.getElementById('chapterSelect');

    if (!reciterSelect.value) {
        alert('يرجى اختيار قارئ أولاً');
        return;
    }

    if (!chapterSelect.value) {
        alert('يرجى اختيار سورة أولاً');
        return;
    }

    const reciterId = reciterSelect.value;
    const chapterId = chapterSelect.value;
    const verseKey = `${chapterId}:1`; // اختبار الآية الأولى

    console.log('🧪 اختبار تنزيل الصوت:', {reciterId, verseKey});

    try {
        const response = await fetch(`http://localhost:5000/api/test_audio/${reciterId}/${verseKey}`);
        const result = await response.json();

        console.log('نتائج اختبار الصوت:', result);

        let message = `🧪 نتائج اختبار تنزيل الصوت:\n\n`;
        message += `القارئ: ${reciterId}\n`;
        message += `الآية: ${verseKey}\n\n`;

        // عرض المصادر التي تعمل فقط
        const workingSources = result.test_results.filter(test => test.accessible);
        if (workingSources.length > 0) {
            message += '✅ المصادر التي تعمل:\n';
            workingSources.forEach((test, index) => {
                message += `${index + 1}. ${test.url}\n`;
            });

            // اختبار تحميل فعلي للمصدر الأول
            message += '\n🔄 اختبار التحميل الفعلي...\n';
            const firstWorkingUrl = workingSources[0].url;

            try {
                const testDownload = await fetch(firstWorkingUrl, {method: 'HEAD'});
                const contentLength = testDownload.headers.get('content-length');
                const contentType = testDownload.headers.get('content-type');

                message += `📊 تفاصيل الملف:\n`;
                message += `الحجم: ${contentLength ? (parseInt(contentLength)/1024).toFixed(1) + ' KB' : 'غير محدد'}\n`;
                message += `النوع: ${contentType || 'غير محدد'}\n`;

                if (contentLength && parseInt(contentLength) > 1000) {
                    message += '✅ الملف يبدو صالحاً للتحميل!';
                } else {
                    message += '⚠️ الملف قد يكون صغير جداً أو تالف';
                }

            } catch (downloadError) {
                message += `❌ خطأ في اختبار التحميل: ${downloadError.message}`;
            }

        } else {
            message += '❌ لا توجد مصادر تعمل';
        }

        // عرض جميع النتائج في Console للتشخيص
        console.log('تفاصيل اختبار الصوت:', result.test_results);

        alert(message);

    } catch (error) {
        console.error('خطأ في اختبار الصوت:', error);
        alert('خطأ في اختبار تنزيل الصوت: ' + error.message);
    }
}

// وظيفة اختبار قراء مشهورين
async function testPopularReciters() {
    const popularReciters = [
        {id: 1, name: "عبد الباسط عبد الصمد"},
        {id: 2, name: "عبد الله بصفر"},
        {id: 3, name: "عبد الرحمن السديس"},
        {id: 7, name: "مشاري راشد العفاسي"},
        {id: 5, name: "محمد صديق المنشاوي"}
    ];

    console.log('🧪 اختبار القراء المشهورين...');

    let results = [];

    for (const reciter of popularReciters) {
        try {
            const response = await fetch(`http://localhost:5000/api/test_audio/${reciter.id}/1:1`);
            const result = await response.json();

            const workingUrls = result.test_results.filter(test => test.accessible).length;
            results.push({
                ...reciter,
                workingUrls: workingUrls,
                totalUrls: result.test_results.length
            });

            console.log(`${reciter.name}: ${workingUrls}/${result.test_results.length} مصادر تعمل`);

        } catch (error) {
            console.error(`خطأ في اختبار ${reciter.name}:`, error);
            results.push({
                ...reciter,
                workingUrls: 0,
                totalUrls: 0,
                error: error.message
            });
        }
    }

    // عرض النتائج
    let message = '🧪 نتائج اختبار القراء المشهورين:\n\n';
    results.forEach(result => {
        if (result.error) {
            message += `❌ ${result.name}: خطأ\n`;
        } else if (result.workingUrls > 0) {
            message += `✅ ${result.name}: ${result.workingUrls}/${result.totalUrls} مصادر تعمل\n`;
        } else {
            message += `❌ ${result.name}: لا توجد مصادر تعمل\n`;
        }
    });

    const bestReciter = results.find(r => r.workingUrls > 0);
    if (bestReciter) {
        message += `\n💡 يُنصح باستخدام: ${bestReciter.name} (ID: ${bestReciter.id})`;
    }

    alert(message);
    console.log('نتائج اختبار القراء:', results);
}

// وظيفة لاختبار URLs مباشرة
function testDirectUrls() {
    const reciterSelect = document.getElementById('reciterSelect');
    const chapterSelect = document.getElementById('chapterSelect');

    if (!reciterSelect.value || !chapterSelect.value) {
        alert('يرجى اختيار سورة وقارئ أولاً');
        return;
    }

    const reciterId = reciterSelect.value;
    const chapterId = chapterSelect.value;
    const verseKey = `${chapterId}:1`;

    // تحويل إلى تنسيقات مختلفة
    const chapterPadded = chapterId.padStart(3, '0');
    const versePadded = '001';

    // خريطة القراء الصحيحة (نفس الخريطة في Backend)
    const reciterFolders = {
        1: "AbdulSamad_64kbps_QuranExplorer.Com",  // عبد الباسط عبد الصمد
        2: "Abdullah_Basfar_192kbps",              // عبد الله بصفر
        3: "abdurrahmaan_as-sudays_64kbps",       // عبد الرحمن السديس
        4: "Abu_Bakr_Ash-Shaatree_128kbps",       // أبو بكر الشاطري
        5: "Alafasy_128kbps",                     // مشاري راشد العفاسي
        6: "maher_almuaiqly_64kbps",              // ماهر المعيقلي
        7: "Alafasy_128kbps",                     // مشاري راشد العفاسي (نسخة أخرى)
        8: "Husary_128kbps",                      // محمود خليل الحصري
        9: "Minshawi_Murattal_128kbps",           // محمد صديق المنشاوي
        10: "Saad_Al-Ghamdi_64kbps"              // سعد الغامدي
    };

    const testUrls = [];

    // إضافة روابط everyayah.com الصحيحة
    if (reciterFolders[reciterId]) {
        const folderName = reciterFolders[reciterId];
        testUrls.push(
            `https://everyayah.com/data/${folderName}/${chapterPadded}${versePadded}.mp3`,
            `https://www.everyayah.com/data/${folderName}/${chapterPadded}${versePadded}.mp3`
        );
    }

    // إضافة مصادر أخرى
    testUrls.push(
        `https://audio.qurancdn.com/ayah/${reciterId}/${verseKey}.mp3`,
        `https://verses.quran.com/${reciterId}/${chapterPadded}${versePadded}.mp3`,
        `https://audio.qurancdn.com/${reciterId}/${chapterPadded}${versePadded}.mp3`
    );

    // إضافة روابط اختبار معروفة للتأكد
    testUrls.push(
        `https://everyayah.com/data/AbdulSamad_64kbps_QuranExplorer.Com/001001.mp3`, // عبد الباسط - الفاتحة آية 1
        `https://everyayah.com/data/Abdullah_Basfar_192kbps/001001.mp3`              // عبد الله بصفر - الفاتحة آية 1
    );

    let message = `🔗 اختبار URLs مباشرة:\n\n`;
    message += `القارئ: ${reciterId}\n`;
    message += `الآية: ${verseKey} (${chapterPadded}${versePadded})\n`;
    if (reciterFolders[reciterId]) {
        message += `مجلد القارئ: ${reciterFolders[reciterId]}\n`;
    }
    message += `\n`;
    message += `URLs للاختبار:\n`;

    testUrls.forEach((url, index) => {
        message += `${index + 1}. ${url}\n`;
    });

    message += `\n💡 انسخ أي رابط واختبره في متصفح جديد`;
    message += `\nإذا عمل الرابط، فالمشكلة في الكود`;
    message += `\nإذا لم يعمل، فالمشكلة في المصدر`;

    // طباعة في Console للنسخ السهل
    console.log('🔗 URLs للاختبار - انسخ من هنا:');
    console.log('=====================================');
    testUrls.forEach((url, index) => {
        console.log(`${index + 1}. ${url}`);
    });
    console.log('=====================================');

    // إنشاء عنصر نص مؤقت للنسخ
    const textArea = document.createElement('textarea');
    textArea.value = testUrls.join('\n');
    document.body.appendChild(textArea);
    textArea.select();

    try {
        document.execCommand('copy');
        message += '\n\n✅ تم نسخ الروابط للحافظة!';
        message += '\n📋 يمكنك لصقها في متصفح جديد';
    } catch (err) {
        message += '\n\n📋 راجع Console لنسخ الروابط يدوياً';
    }

    document.body.removeChild(textArea);

    alert(message);

    // إنشاء نافذة منبثقة مع روابط قابلة للنقر
    const popup = window.open('', 'AudioTestLinks', 'width=800,height=600,scrollbars=yes');
    popup.document.write(`
        <html>
        <head>
            <title>اختبار روابط الصوت</title>
            <style>
                body { font-family: Arial, sans-serif; padding: 20px; direction: rtl; }
                .url-item { margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 5px; }
                .url-link { color: #007bff; text-decoration: none; word-break: break-all; }
                .url-link:hover { text-decoration: underline; }
                .test-btn { background: #28a745; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer; margin-right: 10px; }
            </style>
        </head>
        <body>
            <h2>🔗 اختبار روابط الصوت</h2>
            <p><strong>القارئ:</strong> ${reciterId} | <strong>الآية:</strong> ${verseKey}</p>
            <p>اضغط على أي رابط لاختباره:</p>
            ${testUrls.map((url, index) => `
                <div class="url-item">
                    <strong>${index + 1}.</strong>
                    <button class="test-btn" onclick="window.open('${url}', '_blank')">اختبار</button>
                    <a href="${url}" target="_blank" class="url-link">${url}</a>
                </div>
            `).join('')}
            <br>
            <p><strong>تعليمات:</strong></p>
            <ul>
                <li>اضغط "اختبار" أو على الرابط لفتحه في تبويب جديد</li>
                <li>إذا بدأ تحميل ملف صوتي، فالرابط يعمل ✅</li>
                <li>إذا ظهر خطأ 404، فالرابط لا يعمل ❌</li>
                <li>أخبر المطور بأي رابط يعمل</li>
            </ul>
        </body>
        </html>
    `);
}

// إضافة event listeners عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('تم تحميل الصفحة');

    // إضافة listener للقارئ
    setTimeout(() => {
        const reciterSelect = document.getElementById('reciterSelect');
        if (reciterSelect) {
            reciterSelect.addEventListener('change', function() {
                console.log('تم تغيير القارئ:', this.value);
                updateCreateButton();
                saveSettings();
            });
            console.log('تم إضافة listener للقارئ');
        } else {
            console.error('قائمة القراء غير موجودة عند تحميل الصفحة');
        }

        // تحديث الزر في البداية
        updateCreateButton();

        // تحميل الإعدادات المحفوظة
        loadSavedSettings();

        // إعداد معالجات الصورة المخصصة
        setupCustomImageHandlers();

        // إضافة event listener لاتجاه الفيديو
        const videoOrientationSelect = document.getElementById('videoOrientation');
        if (videoOrientationSelect) {
            videoOrientationSelect.addEventListener('change', function() {
                console.log('📱 تم تغيير اتجاه الفيديو:', this.value);
                saveSettings();
                updateLivePreview(); // تحديث المعاينة عند تغيير الاتجاه
            });
        }

        // تهيئة المعاينة المباشرة
        initializePreview();

        // تهيئة أدوات تخصيص النص
        initializeTextCustomization();
    }, 1000); // انتظار ثانية واحدة لضمان تحميل العناصر
});

// وظائف حفظ واستعادة الإعدادات
function saveSettings() {
    const settings = {
        selectedChapter: document.getElementById('chapterSelect')?.value || '',
        selectedReciter: document.getElementById('reciterSelect')?.value || '',
        videoOrientation: document.getElementById('videoOrientation')?.value || 'landscape',
        timestamp: new Date().getTime()
    };

    localStorage.setItem('quranVideoSettings', JSON.stringify(settings));
    console.log('💾 تم حفظ الإعدادات:', settings);
}

function loadSavedSettings() {
    try {
        const savedSettings = localStorage.getItem('quranVideoSettings');
        if (savedSettings) {
            const settings = JSON.parse(savedSettings);
            console.log('📂 تم تحميل الإعدادات المحفوظة:', settings);

            // استعادة الإعدادات
            setTimeout(() => {
                if (settings.selectedChapter) {
                    const chapterSelect = document.getElementById('chapterSelect');
                    if (chapterSelect && chapterSelect.options.length > 1) {
                        chapterSelect.value = settings.selectedChapter;
                        loadVerses();
                        console.log('📖 تم استعادة السورة:', settings.selectedChapter);
                    }
                }

                if (settings.selectedReciter) {
                    const reciterSelect = document.getElementById('reciterSelect');
                    if (reciterSelect && reciterSelect.options.length > 1) {
                        reciterSelect.value = settings.selectedReciter;
                        updateCreateButton();
                        console.log('🎵 تم استعادة القارئ:', settings.selectedReciter);
                    }
                }

                if (settings.videoOrientation) {
                    const videoOrientationSelect = document.getElementById('videoOrientation');
                    if (videoOrientationSelect) {
                        videoOrientationSelect.value = settings.videoOrientation;
                        console.log('📱 تم استعادة اتجاه الفيديو:', settings.videoOrientation);
                    }
                }
            }, 2000); // انتظار أطول لضمان تحميل القوائم
        }
    } catch (error) {
        console.error('خطأ في تحميل الإعدادات:', error);
    }
}

// وظائف شريط التقدم
function updateProgress(percentage, step) {
    const progressFill = document.getElementById('progressFill');
    const progressText = document.getElementById('progressText');

    if (progressFill) {
        progressFill.style.width = percentage + '%';
    }

    if (progressText) {
        progressText.textContent = Math.round(percentage) + '%';
    }

    // تحديث الخطوات
    updateSteps(step);
}

function updateSteps(currentStep) {
    const steps = ['step1', 'step2', 'step3', 'step4'];

    steps.forEach((stepId, index) => {
        const stepElement = document.getElementById(stepId);
        if (stepElement) {
            stepElement.classList.remove('active', 'completed');

            if (index < currentStep) {
                stepElement.classList.add('completed');
            } else if (index === currentStep) {
                stepElement.classList.add('active');
            }
        }
    });
}

function simulateProgress() {
    let progress = 0;
    let step = 0;

    const interval = setInterval(() => {
        progress += Math.random() * 15 + 5; // زيادة عشوائية بين 5-20%

        if (progress >= 25 && step === 0) step = 1;
        if (progress >= 50 && step === 1) step = 2;
        if (progress >= 75 && step === 2) step = 3;
        if (progress >= 95) step = 4;

        if (progress >= 100) {
            progress = 100;
            clearInterval(interval);
        }

        updateProgress(progress, step);
    }, 800);
}

// متغير لحفظ مسار الصورة المخصصة
let customImagePath = null;

// وظائف معالجة الصورة المخصصة
function setupCustomImageHandlers() {
    const customImageInput = document.getElementById('customImageInput');
    const imagePreview = document.getElementById('imagePreview');
    const previewImg = document.getElementById('previewImg');
    const imageInfo = document.getElementById('imageInfo');
    const removeImageBtn = document.getElementById('removeImageBtn');

    if (customImageInput) {
        customImageInput.addEventListener('change', handleCustomImageUpload);
    }

    if (removeImageBtn) {
        removeImageBtn.addEventListener('click', removeCustomImage);
    }
}

async function handleCustomImageUpload(event) {
    const file = event.target.files[0];
    if (!file) return;

    // التحقق من نوع الملف
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/bmp'];
    if (!allowedTypes.includes(file.type)) {
        alert('نوع الملف غير مدعوم. يرجى اختيار صورة (JPG, PNG, GIF, BMP)');
        event.target.value = '';
        return;
    }

    // التحقق من حجم الملف (أقل من 10 ميجابايت)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
        alert('حجم الملف كبير جداً. يرجى اختيار صورة أصغر من 10 ميجابايت');
        event.target.value = '';
        return;
    }

    try {
        // عرض معاينة محلية
        const reader = new FileReader();
        reader.onload = function(e) {
            const previewImg = document.getElementById('previewImg');
            const imageInfo = document.getElementById('imageInfo');
            const imagePreview = document.getElementById('imagePreview');

            if (previewImg && imageInfo && imagePreview) {
                previewImg.src = e.target.result;
                imageInfo.textContent = `${file.name} (${(file.size / 1024).toFixed(1)} KB)`;
                imagePreview.style.display = 'block';
            }
        };
        reader.readAsDataURL(file);

        // رفع الصورة للخادم
        console.log('🖼️ بدء رفع الصورة المخصصة...');
        const formData = new FormData();
        formData.append('image', file);

        const response = await fetch('http://localhost:5000/upload_custom_image', {
            method: 'POST',
            body: formData
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || 'فشل في رفع الصورة');
        }

        const result = await response.json();
        customImagePath = result.image_path;

        console.log('✅ تم رفع الصورة بنجاح:', result);

        // تحديث معلومات الصورة
        const imageInfo = document.getElementById('imageInfo');
        if (imageInfo) {
            imageInfo.textContent = `${file.name} (${(result.size / 1024).toFixed(1)} KB) - ${result.dimensions.width}x${result.dimensions.height}`;
        }

        // إظهار رسالة نجاح
        showSuccessMessage('تم رفع الصورة بنجاح! ✅');

        // تحميل الصورة للمعاينة المباشرة
        loadCustomImageForPreview(file);

    } catch (error) {
        console.error('خطأ في رفع الصورة:', error);
        alert('خطأ في رفع الصورة: ' + error.message);
        event.target.value = '';
        removeCustomImage();
    }
}

function removeCustomImage() {
    const customImageInput = document.getElementById('customImageInput');
    const imagePreview = document.getElementById('imagePreview');

    if (customImageInput) {
        customImageInput.value = '';
    }

    if (imagePreview) {
        imagePreview.style.display = 'none';
    }

    customImagePath = null;
    currentPreviewImage = null;
    console.log('🗑️ تم إزالة الصورة المخصصة');

    // تحديث المعاينة المباشرة
    updateLivePreview();
}

function showSuccessMessage(message) {
    // إنشاء رسالة نجاح مؤقتة
    const successDiv = document.createElement('div');
    successDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #28a745;
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        z-index: 1000;
        font-weight: 600;
        animation: slideIn 0.3s ease;
    `;
    successDiv.textContent = message;

    document.body.appendChild(successDiv);

    // إزالة الرسالة بعد 3 ثواني
    setTimeout(() => {
        successDiv.style.animation = 'slideOut 0.3s ease';
        setTimeout(() => {
            if (successDiv.parentNode) {
                successDiv.parentNode.removeChild(successDiv);
            }
        }, 300);
    }, 3000);
}

// متغيرات المعاينة المباشرة
let previewCanvas = null;
let previewContext = null;
let currentPreviewImage = null;

// إعدادات تخصيص النص
let textSettings = {
    fontSize: 36,
    textColor: '#ffffff',
    fontWeight: 'bold',
    fontFamily: 'Amiri',
    textPosition: 'bottom',
    textBgOpacity: 70,
    textBgColor: '#000000',
    textShadow: 6,
    textPadding: 40
};

// وظائف المعاينة المباشرة
function initializePreview() {
    previewCanvas = document.getElementById('previewCanvas');
    if (previewCanvas) {
        previewContext = previewCanvas.getContext('2d');

        // إضافة إمكانية النقر لتكبير المعاينة
        previewCanvas.addEventListener('click', function() {
            openPreviewModal();
        });

        // إضافة tooltip
        previewCanvas.title = 'انقر لتكبير المعاينة';
    }
}

function updateLivePreview() {
    // التحقق من وجود آيات مختارة
    if (selectedVerses.length === 0) {
        hideLivePreview();
        return;
    }

    // الحصول على اتجاه الفيديو
    const videoOrientation = document.getElementById('videoOrientation').value || 'landscape';
    const dimensions = getVideoDimensions(videoOrientation);

    // إظهار المعاينة
    showLivePreview();

    // إنشاء المعاينة
    createPreviewImage(dimensions);
}

function getVideoDimensions(orientation) {
    const dimensions = {
        'landscape': { width: 1280, height: 720, name: 'أفقي (HD)' },
        'portrait': { width: 720, height: 1280, name: 'عمودي (موبايل)' },
        'square': { width: 1080, height: 1080, name: 'مربع (وسائل التواصل)' }
    };

    return dimensions[orientation] || dimensions['landscape'];
}

function showLivePreview() {
    const livePreview = document.getElementById('livePreview');
    if (livePreview) {
        livePreview.style.display = 'block';
    }
}

function hideLivePreview() {
    const livePreview = document.getElementById('livePreview');
    if (livePreview) {
        livePreview.style.display = 'none';
    }
}

function createPreviewImage(dimensions) {
    if (!previewCanvas || !previewContext) {
        initializePreview();
        if (!previewCanvas || !previewContext) return;
    }

    // تعيين أبعاد الكانفاس - حجم أكبر للوضوح
    const maxDisplayWidth = 600;
    const maxDisplayHeight = 500;
    const scale = Math.min(maxDisplayWidth / dimensions.width, maxDisplayHeight / dimensions.height);
    const canvasWidth = dimensions.width * scale;
    const canvasHeight = dimensions.height * scale;

    previewCanvas.width = dimensions.width;
    previewCanvas.height = dimensions.height;
    previewCanvas.style.width = canvasWidth + 'px';
    previewCanvas.style.height = canvasHeight + 'px';

    // مسح الكانفاس
    previewContext.clearRect(0, 0, dimensions.width, dimensions.height);

    // إنشاء الخلفية
    if (customImagePath && currentPreviewImage) {
        // استخدام الصورة المخصصة
        drawCustomImagePreview(dimensions);
    } else {
        // إنشاء خلفية افتراضية
        drawDefaultBackground(dimensions);
    }

    // إضافة النص القرآني
    drawVersesText(dimensions);

    // تحديث معلومات المعاينة
    updatePreviewInfo(dimensions);
}

function drawCustomImagePreview(dimensions) {
    if (!currentPreviewImage) return;

    // حساب النسبة للحفاظ على شكل الصورة
    const ratio = Math.min(dimensions.width / currentPreviewImage.width, dimensions.height / currentPreviewImage.height);
    const resizedWidth = currentPreviewImage.width * ratio;
    const resizedHeight = currentPreviewImage.height * ratio;

    // توسيط الصورة
    const x = (dimensions.width - resizedWidth) / 2;
    const y = (dimensions.height - resizedHeight) / 2;

    // رسم خلفية سوداء
    previewContext.fillStyle = '#000000';
    previewContext.fillRect(0, 0, dimensions.width, dimensions.height);

    // رسم الصورة
    previewContext.drawImage(currentPreviewImage, x, y, resizedWidth, resizedHeight);
}

function drawDefaultBackground(dimensions) {
    // إنشاء تدرج لوني جميل
    const gradient = previewContext.createLinearGradient(0, 0, dimensions.width, dimensions.height);
    gradient.addColorStop(0, '#1e3c72');
    gradient.addColorStop(0.5, '#2a5298');
    gradient.addColorStop(1, '#0f4c75');

    previewContext.fillStyle = gradient;
    previewContext.fillRect(0, 0, dimensions.width, dimensions.height);

    // إضافة نمط إسلامي
    drawIslamicPattern(dimensions);
}

function drawIslamicPattern(dimensions) {
    previewContext.save();
    previewContext.globalAlpha = 0.1;
    previewContext.strokeStyle = '#FFD700';
    previewContext.lineWidth = 2;

    // رسم دوائر متداخلة
    const centerX = dimensions.width / 2;
    const centerY = dimensions.height / 2;
    const maxRadius = Math.min(dimensions.width, dimensions.height) / 3;

    for (let i = 1; i <= 3; i++) {
        previewContext.beginPath();
        previewContext.arc(centerX, centerY, maxRadius * i / 3, 0, 2 * Math.PI);
        previewContext.stroke();
    }

    previewContext.restore();
}

function drawVersesText(dimensions) {
    if (selectedVerses.length === 0) return;

    // جمع نصوص الآيات مع أرقامها داخل الفاصل
    const versesWithNumbers = selectedVerses.map((verse) => {
        // تنظيف النص من أي فواصل موجودة مسبقاً
        const cleanText = verse.text_uthmani.replace(/\s*۝\s*/g, '').trim();

        // إضافة رقم الآية الحالية مع فاصل واحد فقط
        const currentVerseNumber = convertToArabicNumbers(verse.verse_number);
        return cleanText + ' ۝' + currentVerseNumber;
    });
    const versesText = versesWithNumbers.join(' ');

    // استخدام إعدادات النص المخصصة - نفس خط الآيات المختارة
    const fontSize = textSettings.fontSize;
    const fontFamily = textSettings.fontFamily || 'Amiri';

    // تحديد الخط حسب الاختيار
    let fontString;
    switch(fontFamily) {
        case 'Uthmanic':
            fontString = `${textSettings.fontWeight} ${fontSize}px 'Uthmanic', 'Amiri', Arial, sans-serif`;
            break;
        case 'Arial':
            fontString = `${textSettings.fontWeight} ${fontSize}px Arial, sans-serif`;
            break;
        case 'Tahoma':
            fontString = `${textSettings.fontWeight} ${fontSize}px Tahoma, Arial, sans-serif`;
            break;
        case 'Amiri':
        default:
            fontString = `${textSettings.fontWeight} ${fontSize}px 'Amiri', Arial, sans-serif`;
            break;
    }

    previewContext.font = fontString;
    console.log('🔤 خط المعاينة المستخدم:', fontString);
    previewContext.textAlign = 'center';
    previewContext.textBaseline = 'middle';

    // تقسيم النص إلى أسطر
    const lines = wrapText(previewContext, versesText, dimensions.width - (textSettings.textPadding * 2));

    // حساب موقع النص حسب الإعداد المختار
    const lineHeight = fontSize + 10;
    const totalTextHeight = lines.length * lineHeight;
    let startY;

    switch (textSettings.textPosition) {
        case 'top':
            startY = textSettings.textPadding;
            break;
        case 'center':
            startY = (dimensions.height - totalTextHeight) / 2;
            break;
        case 'bottom-center':
            startY = dimensions.height - totalTextHeight - (textSettings.textPadding * 2);
            break;
        case 'bottom':
        default:
            startY = dimensions.height - totalTextHeight - textSettings.textPadding;
            break;
    }

    // رسم خلفية شبه شفافة للنص مع تدرج
    const bgOpacity = textSettings.textBgOpacity / 100;
    const bgColor = hexToRgb(textSettings.textBgColor);

    const textBgGradient = previewContext.createLinearGradient(0, startY - 30, 0, startY + totalTextHeight + 30);
    textBgGradient.addColorStop(0, `rgba(${bgColor.r}, ${bgColor.g}, ${bgColor.b}, ${bgOpacity * 0.3})`);
    textBgGradient.addColorStop(0.5, `rgba(${bgColor.r}, ${bgColor.g}, ${bgColor.b}, ${bgOpacity})`);
    textBgGradient.addColorStop(1, `rgba(${bgColor.r}, ${bgColor.g}, ${bgColor.b}, ${bgOpacity * 0.3})`);

    previewContext.fillStyle = textBgGradient;
    previewContext.fillRect(0, startY - 30, dimensions.width, totalTextHeight + 60);

    // رسم النص مع ظل محسن
    previewContext.fillStyle = textSettings.textColor;
    previewContext.shadowColor = 'rgba(0, 0, 0, 0.9)';
    previewContext.shadowBlur = textSettings.textShadow;
    previewContext.shadowOffsetX = textSettings.textShadow / 2;
    previewContext.shadowOffsetY = textSettings.textShadow / 2;

    // رسم النص العادي (الزخرفة الأصلية ستتعامل مع أرقام الآيات)
    lines.forEach((line, index) => {
        const y = startY + index * lineHeight;
        previewContext.fillText(line, dimensions.width / 2, y);
    });

    // إزالة الظل
    previewContext.shadowColor = 'transparent';
}

function wrapText(context, text, maxWidth) {
    const words = text.split(' ');
    const lines = [];
    let currentLine = '';

    for (let word of words) {
        const testLine = currentLine + (currentLine ? ' ' : '') + word;
        const metrics = context.measureText(testLine);

        if (metrics.width > maxWidth && currentLine) {
            lines.push(currentLine);
            currentLine = word;
        } else {
            currentLine = testLine;
        }
    }

    if (currentLine) {
        lines.push(currentLine);
    }

    // لا نعكس الترتيب - الترتيب الطبيعي صحيح
    return lines;
}

function updatePreviewInfo(dimensions) {
    const previewInfo = document.getElementById('previewInfo');
    if (previewInfo) {
        const versesCount = selectedVerses.length;
        const imageSource = customImagePath ? 'صورة مخصصة' : 'خلفية افتراضية';

        previewInfo.textContent = `${dimensions.name} (${dimensions.width}x${dimensions.height}) • ${versesCount} آية • ${imageSource}`;
    }
}

function loadCustomImageForPreview(file) {
    const reader = new FileReader();
    reader.onload = function(e) {
        const img = new Image();
        img.onload = function() {
            currentPreviewImage = img;
            updateLivePreview();
        };
        img.src = e.target.result;
    };
    reader.readAsDataURL(file);
}

// وظيفة فتح نافذة تكبير المعاينة
function openPreviewModal() {
    if (!previewCanvas) return;

    // إنشاء نافذة منبثقة
    const modal = document.createElement('div');
    modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.9);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 10000;
        cursor: pointer;
    `;

    // إنشاء كانفاس مكبر
    const enlargedCanvas = document.createElement('canvas');
    const videoOrientation = document.getElementById('videoOrientation').value || 'landscape';
    const dimensions = getVideoDimensions(videoOrientation);

    // حساب أبعاد التكبير (90% من الشاشة)
    const maxWidth = window.innerWidth * 0.9;
    const maxHeight = window.innerHeight * 0.9;
    const scale = Math.min(maxWidth / dimensions.width, maxHeight / dimensions.height);

    enlargedCanvas.width = dimensions.width;
    enlargedCanvas.height = dimensions.height;
    enlargedCanvas.style.width = (dimensions.width * scale) + 'px';
    enlargedCanvas.style.height = (dimensions.height * scale) + 'px';
    enlargedCanvas.style.border = '3px solid #FFD700';
    enlargedCanvas.style.borderRadius = '12px';
    enlargedCanvas.style.boxShadow = '0 10px 30px rgba(0, 0, 0, 0.5)';

    // نسخ المحتوى من الكانفاس الأصلي
    const enlargedContext = enlargedCanvas.getContext('2d');
    enlargedContext.drawImage(previewCanvas, 0, 0);

    // إضافة معلومات إضافية
    const infoDiv = document.createElement('div');
    infoDiv.style.cssText = `
        position: absolute;
        bottom: 20px;
        left: 50%;
        transform: translateX(-50%);
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 15px 25px;
        border-radius: 25px;
        font-size: 16px;
        font-weight: 600;
        text-align: center;
        border: 2px solid #FFD700;
    `;

    const versesCount = selectedVerses.length;
    const imageSource = customImagePath ? 'صورة مخصصة' : 'خلفية افتراضية';
    infoDiv.innerHTML = `
        <div>🔍 معاينة مكبرة</div>
        <div style="font-size: 14px; margin-top: 5px; opacity: 0.9;">
            ${dimensions.name} (${dimensions.width}x${dimensions.height}) • ${versesCount} آية • ${imageSource}
        </div>
        <div style="font-size: 12px; margin-top: 8px; opacity: 0.7;">
            انقر في أي مكان للإغلاق
        </div>
    `;

    // تجميع العناصر
    const container = document.createElement('div');
    container.style.position = 'relative';
    container.appendChild(enlargedCanvas);
    container.appendChild(infoDiv);

    modal.appendChild(container);

    // إضافة للصفحة
    document.body.appendChild(modal);

    // إضافة تأثير الظهور
    modal.style.opacity = '0';
    setTimeout(() => {
        modal.style.transition = 'opacity 0.3s ease';
        modal.style.opacity = '1';
    }, 10);

    // إغلاق عند النقر
    modal.addEventListener('click', function() {
        modal.style.opacity = '0';
        setTimeout(() => {
            if (modal.parentNode) {
                modal.parentNode.removeChild(modal);
            }
        }, 300);
    });

    // إغلاق بمفتاح Escape
    const handleEscape = function(e) {
        if (e.key === 'Escape') {
            modal.click();
            document.removeEventListener('keydown', handleEscape);
        }
    };
    document.addEventListener('keydown', handleEscape);
}

// وظائف تخصيص النص
function initializeTextCustomization() {
    // إعداد event listeners للتحكم في النص
    const fontSize = document.getElementById('fontSize');
    const textColor = document.getElementById('textColor');
    const fontWeight = document.getElementById('fontWeight');
    const textPosition = document.getElementById('textPosition');
    const textBgOpacity = document.getElementById('textBgOpacity');
    const textBgColor = document.getElementById('textBgColor');
    const textShadow = document.getElementById('textShadow');
    const textPadding = document.getElementById('textPadding');

    if (fontSize) {
        fontSize.addEventListener('input', function() {
            textSettings.fontSize = parseInt(this.value);
            document.getElementById('fontSizeValue').textContent = this.value + 'px';
            updateLivePreview();
        });
    }

    if (textColor) {
        textColor.addEventListener('change', function() {
            textSettings.textColor = this.value;
            updateLivePreview();
        });
    }

    if (fontWeight) {
        fontWeight.addEventListener('change', function() {
            textSettings.fontWeight = this.value;
            updateLivePreview();
        });
    }

    const fontFamily = document.getElementById('fontFamily');
    if (fontFamily) {
        fontFamily.addEventListener('change', function() {
            textSettings.fontFamily = this.value;
            console.log('🔤 تم تغيير نوع الخط إلى:', this.value);
            updateLivePreview();
        });
    }

    if (textPosition) {
        textPosition.addEventListener('change', function() {
            textSettings.textPosition = this.value;
            updateLivePreview();
        });
    }

    if (textBgOpacity) {
        textBgOpacity.addEventListener('input', function() {
            textSettings.textBgOpacity = parseInt(this.value);
            document.getElementById('textBgOpacityValue').textContent = this.value + '%';
            updateLivePreview();
        });
    }

    if (textBgColor) {
        textBgColor.addEventListener('change', function() {
            textSettings.textBgColor = this.value;
            updateLivePreview();
        });
    }

    if (textShadow) {
        textShadow.addEventListener('input', function() {
            textSettings.textShadow = parseInt(this.value);
            document.getElementById('textShadowValue').textContent = this.value + 'px';
            updateLivePreview();
        });
    }

    if (textPadding) {
        textPadding.addEventListener('input', function() {
            textSettings.textPadding = parseInt(this.value);
            document.getElementById('textPaddingValue').textContent = this.value + 'px';
            updateLivePreview();
        });
    }
}

function setTextColor(color) {
    document.getElementById('textColor').value = color;
    textSettings.textColor = color;
    updateLivePreview();
}

function setTextBgColor(color) {
    document.getElementById('textBgColor').value = color;
    textSettings.textBgColor = color;
    updateLivePreview();
}

function applyPreset(presetName) {
    const presets = {
        classic: {
            fontSize: 32,
            textColor: '#ffffff',
            fontWeight: 'bold',
            textPosition: 'bottom',
            textBgOpacity: 80,
            textBgColor: '#000000',
            textShadow: 4,
            textPadding: 40
        },
        modern: {
            fontSize: 40,
            textColor: '#ffd700',
            fontWeight: 'bolder',
            textPosition: 'bottom-center',
            textBgOpacity: 60,
            textBgColor: '#1e3c72',
            textShadow: 8,
            textPadding: 50
        },
        elegant: {
            fontSize: 36,
            textColor: '#f8f9fa',
            fontWeight: 'normal',
            textPosition: 'bottom',
            textBgOpacity: 75,
            textBgColor: '#2d5016',
            textShadow: 6,
            textPadding: 60
        },
        bold: {
            fontSize: 48,
            textColor: '#ffffff',
            fontWeight: 'bolder',
            textPosition: 'center',
            textBgOpacity: 90,
            textBgColor: '#000000',
            textShadow: 10,
            textPadding: 30
        }
    };

    const preset = presets[presetName];
    if (preset) {
        // تطبيق الإعدادات
        Object.assign(textSettings, preset);

        // تحديث واجهة المستخدم
        updateTextCustomizationUI();

        // تحديث المعاينة
        updateLivePreview();

        console.log(`تم تطبيق الإعداد المسبق: ${presetName}`);
    }
}

function resetTextSettings() {
    // إعادة تعيين الإعدادات للقيم الافتراضية
    textSettings = {
        fontSize: 36,
        textColor: '#ffffff',
        fontWeight: 'bold',
        fontFamily: 'Amiri',
        textPosition: 'bottom',
        textBgOpacity: 70,
        textBgColor: '#000000',
        textShadow: 6,
        textPadding: 40
    };

    // تحديث واجهة المستخدم
    updateTextCustomizationUI();

    // تحديث المعاينة
    updateLivePreview();

    console.log('تم إعادة تعيين إعدادات النص');
}

function updateTextCustomizationUI() {
    // تحديث جميع عناصر واجهة المستخدم
    const elements = {
        fontSize: document.getElementById('fontSize'),
        fontSizeValue: document.getElementById('fontSizeValue'),
        textColor: document.getElementById('textColor'),
        fontWeight: document.getElementById('fontWeight'),
        fontFamily: document.getElementById('fontFamily'),
        textPosition: document.getElementById('textPosition'),
        textBgOpacity: document.getElementById('textBgOpacity'),
        textBgOpacityValue: document.getElementById('textBgOpacityValue'),
        textBgColor: document.getElementById('textBgColor'),
        textShadow: document.getElementById('textShadow'),
        textShadowValue: document.getElementById('textShadowValue'),
        textPadding: document.getElementById('textPadding'),
        textPaddingValue: document.getElementById('textPaddingValue')
    };

    if (elements.fontSize) {
        elements.fontSize.value = textSettings.fontSize;
        elements.fontSizeValue.textContent = textSettings.fontSize + 'px';
    }

    if (elements.textColor) {
        elements.textColor.value = textSettings.textColor;
    }

    if (elements.fontWeight) {
        elements.fontWeight.value = textSettings.fontWeight;
    }

    if (elements.fontFamily) {
        elements.fontFamily.value = textSettings.fontFamily;
    }

    if (elements.textPosition) {
        elements.textPosition.value = textSettings.textPosition;
    }

    if (elements.textBgOpacity) {
        elements.textBgOpacity.value = textSettings.textBgOpacity;
        elements.textBgOpacityValue.textContent = textSettings.textBgOpacity + '%';
    }

    if (elements.textBgColor) {
        elements.textBgColor.value = textSettings.textBgColor;
    }

    if (elements.textShadow) {
        elements.textShadow.value = textSettings.textShadow;
        elements.textShadowValue.textContent = textSettings.textShadow + 'px';
    }

    if (elements.textPadding) {
        elements.textPadding.value = textSettings.textPadding;
        elements.textPaddingValue.textContent = textSettings.textPadding + 'px';
    }
}

function showTextCustomization() {
    const textCustomization = document.getElementById('textCustomization');
    if (textCustomization) {
        textCustomization.style.display = 'block';
    }
}

function hideTextCustomization() {
    const textCustomization = document.getElementById('textCustomization');
    if (textCustomization) {
        textCustomization.style.display = 'none';
    }
}

// وظيفة مساعدة لتحويل hex إلى rgb
function hexToRgb(hex) {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? {
        r: parseInt(result[1], 16),
        g: parseInt(result[2], 16),
        b: parseInt(result[3], 16)
    } : { r: 0, g: 0, b: 0 };
}

// وظيفة تحويل الأرقام إلى الأرقام الهندية
function convertToArabicNumbers(number) {
    const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    return number.toString().split('').map(digit => arabicNumbers[parseInt(digit)]).join('');
}

// قائمة الآيات الطويلة والجميلة
const longVerses = [
    // آية الكرسي
    { chapter: 2, verse: 255 },
    // آية النور
    { chapter: 24, verse: 35 },
    // آية المداينة (أطول آية في القرآن)
    { chapter: 2, verse: 282 },
    // من سورة البقرة
    { chapter: 2, verse: 164 },
    { chapter: 2, verse: 177 },
    { chapter: 2, verse: 197 },
    // من سورة آل عمران
    { chapter: 3, verse: 190 },
    { chapter: 3, verse: 191 },
    { chapter: 3, verse: 26 },
    { chapter: 3, verse: 27 },
    // من سورة النساء
    { chapter: 4, verse: 1 },
    { chapter: 4, verse: 36 },
    // من سورة المائدة
    { chapter: 5, verse: 32 },
    { chapter: 5, verse: 54 },
    // من سورة الأنعام
    { chapter: 6, verse: 59 },
    { chapter: 6, verse: 95 },
    { chapter: 6, verse: 125 },
    // من سورة الأعراف
    { chapter: 7, verse: 54 },
    { chapter: 7, verse: 156 },
    // من سورة الأنفال
    { chapter: 8, verse: 1 },
    { chapter: 8, verse: 24 },
    { chapter: 8, verse: 46 },
    // من سورة التوبة
    { chapter: 9, verse: 51 },
    { chapter: 9, verse: 129 },
    // من سورة يونس
    { chapter: 10, verse: 57 },
    { chapter: 10, verse: 61 },
    // من سورة هود
    { chapter: 11, verse: 56 },
    { chapter: 11, verse: 88 },
    // من سورة الرعد
    { chapter: 13, verse: 28 },
    { chapter: 13, verse: 11 },
    // من سورة إبراهيم
    { chapter: 14, verse: 7 },
    { chapter: 14, verse: 34 },
    // من سورة الحجر
    { chapter: 15, verse: 99 },
    // من سورة النحل
    { chapter: 16, verse: 90 },
    { chapter: 16, verse: 125 },
    // من سورة الإسراء
    { chapter: 17, verse: 44 },
    { chapter: 17, verse: 70 },
    { chapter: 17, verse: 110 },
    // من سورة الكهف
    { chapter: 18, verse: 29 },
    { chapter: 18, verse: 46 },
    // من سورة الحج
    { chapter: 22, verse: 46 },
    { chapter: 22, verse: 78 },
    // من سورة المؤمنون
    { chapter: 23, verse: 14 },
    // من سورة الفرقان
    { chapter: 25, verse: 63 },
    { chapter: 25, verse: 74 },
    // من سورة الشعراء
    { chapter: 26, verse: 80 },
    // من سورة الروم
    { chapter: 30, verse: 21 },
    { chapter: 30, verse: 22 },
    // من سورة لقمان
    { chapter: 31, verse: 27 },
    // من سورة السجدة
    { chapter: 32, verse: 16 },
    // من سورة الأحزاب
    { chapter: 33, verse: 35 },
    { chapter: 33, verse: 56 },
    // من سورة فاطر
    { chapter: 35, verse: 28 },
    // من سورة الزمر
    { chapter: 39, verse: 53 },
    { chapter: 39, verse: 23 },
    // من سورة الشورى
    { chapter: 42, verse: 30 },
    { chapter: 42, verse: 36 },
    { chapter: 42, verse: 37 },
    { chapter: 42, verse: 38 },
    // من سورة الحديد
    { chapter: 57, verse: 20 },
    { chapter: 57, verse: 21 },
    // من سورة الحشر
    { chapter: 59, verse: 22 },
    { chapter: 59, verse: 23 },
    { chapter: 59, verse: 24 },
    // من سورة الجمعة
    { chapter: 62, verse: 2 },
    // من سورة المنافقون
    { chapter: 63, verse: 9 },
    // من سورة التغابن
    { chapter: 64, verse: 11 },
    { chapter: 64, verse: 15 },
    { chapter: 64, verse: 16 },
    // من سورة الطلاق
    { chapter: 65, verse: 2 },
    { chapter: 65, verse: 3 },
    // من سورة التحريم
    { chapter: 66, verse: 6 },
    { chapter: 66, verse: 8 },
    // من سورة الملك
    { chapter: 67, verse: 2 },
    { chapter: 67, verse: 15 },
    // من سورة القلم
    { chapter: 68, verse: 4 },
    // من سورة المزمل
    { chapter: 73, verse: 20 },
    // من سورة المدثر
    { chapter: 74, verse: 38 },
    // من سورة الإنسان
    { chapter: 76, verse: 29 },
    { chapter: 76, verse: 30 },
    // من سورة الفجر
    { chapter: 89, verse: 27 },
    { chapter: 89, verse: 28 },
    { chapter: 89, verse: 29 },
    { chapter: 89, verse: 30 },
    // من سورة البلد
    { chapter: 90, verse: 4 },
    // من سورة الشمس
    { chapter: 91, verse: 7 },
    { chapter: 91, verse: 8 },
    { chapter: 91, verse: 9 },
    { chapter: 91, verse: 10 },
    // من سورة الليل
    { chapter: 92, verse: 4 },
    { chapter: 92, verse: 5 },
    { chapter: 92, verse: 6 },
    { chapter: 92, verse: 7 },
    // من سورة الضحى
    { chapter: 93, verse: 6 },
    { chapter: 93, verse: 7 },
    { chapter: 93, verse: 8 },
    // من سورة الشرح
    { chapter: 94, verse: 5 },
    { chapter: 94, verse: 6 },
    // من سورة العلق
    { chapter: 96, verse: 1 },
    { chapter: 96, verse: 2 },
    { chapter: 96, verse: 3 },
    { chapter: 96, verse: 4 },
    { chapter: 96, verse: 5 }
];

// وظيفة اختيار آية عشوائية طويلة
async function selectRandomVerse() {
    try {
        // إظهار رسالة تحميل
        const randomBtn = document.getElementById('randomVerseBtn');
        const originalText = randomBtn.innerHTML;
        randomBtn.innerHTML = '🔄 جاري البحث...';
        randomBtn.disabled = true;

        // اختيار آية عشوائية من القائمة
        const randomIndex = Math.floor(Math.random() * longVerses.length);
        const selectedVerse = longVerses[randomIndex];

        console.log(`🎲 تم اختيار آية عشوائية: سورة ${selectedVerse.chapter} آية ${selectedVerse.verse}`);

        // تحميل الآية مباشرة من API
        const verseKey = `${selectedVerse.chapter}:${selectedVerse.verse}`;
        const response = await fetch(`https://api.quran.com/api/v4/verses/by_key/${verseKey}?translations=131&fields=text_uthmani`);

        if (!response.ok) {
            throw new Error(`فشل في تحميل الآية ${verseKey}`);
        }

        const data = await response.json();
        const verseData = data.verse;

        if (!verseData) {
            throw new Error(`لم يتم العثور على الآية ${verseKey}`);
        }

        // إنشاء بيانات الآية
        const verseObject = {
            chapter_number: selectedVerse.chapter,
            verse_number: selectedVerse.verse,
            text_uthmani: verseData.text_uthmani,
            verse_key: verseKey
        };

        // تحديث الآيات المختارة مباشرة
        selectedVerses = [verseObject];

        // تحديث المعاينة مباشرة
        updateVersesPreview();
        updateLivePreview();
        updateCreateButton();

        // إظهار أدوات التخصيص
        showTextCustomization();

        // تحديث اختيار السورة (اختياري)
        const chapterSelect = document.getElementById('chapterSelect');
        if (chapterSelect.value !== selectedVerse.chapter.toString()) {
            chapterSelect.value = selectedVerse.chapter;
        }

        console.log('✅ تم تحديد الآية العشوائية بنجاح');
        console.log('📖 نص الآية:', verseObject.text_uthmani.substring(0, 50) + '...');

        // إظهار رسالة نجاح
        const chapterName = await getChapterNameFromAPI(selectedVerse.chapter);
        showSuccess(`🎲 تم اختيار آية جميلة من سورة ${chapterName} - آية ${selectedVerse.verse}`);

        // استعادة النص الأصلي للزر
        randomBtn.innerHTML = originalText;
        randomBtn.disabled = false;

    } catch (error) {
        console.error('❌ خطأ في اختيار الآية العشوائية:', error);
        showError('حدث خطأ في اختيار الآية العشوائية: ' + error.message);

        // استعادة النص الأصلي للزر
        const randomBtn = document.getElementById('randomVerseBtn');
        randomBtn.innerHTML = '🎲 آية عشوائية طويلة';
        randomBtn.disabled = false;
    }
}

// وظيفة مساعدة للحصول على اسم السورة
function getChapterName(chapterNumber) {
    const chapterSelect = document.getElementById('chapterSelect');
    const option = chapterSelect.querySelector(`option[value="${chapterNumber}"]`);
    return option ? option.textContent.split(' - ')[0] : `السورة ${chapterNumber}`;
}

// وظيفة للحصول على اسم السورة من API
async function getChapterNameFromAPI(chapterNumber) {
    try {
        // محاولة الحصول على الاسم من القائمة المحملة أولاً
        const localName = getChapterName(chapterNumber);
        if (localName !== `السورة ${chapterNumber}`) {
            return localName;
        }

        // إذا لم يكن متوفراً محلياً، جلبه من API
        const response = await fetch(`https://api.quran.com/api/v4/chapters/${chapterNumber}?language=ar`);
        if (response.ok) {
            const data = await response.json();
            return data.chapter.name_arabic || `السورة ${chapterNumber}`;
        }
    } catch (error) {
        console.log('خطأ في جلب اسم السورة:', error);
    }

    // fallback
    return `السورة ${chapterNumber}`;
}

// وظيفة إظهار رسالة نجاح
function showSuccess(message) {
    // إنشاء عنصر الرسالة
    const successDiv = document.createElement('div');
    successDiv.className = 'success-message';
    successDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: linear-gradient(135deg, #28a745, #20c997);
        color: white;
        padding: 15px 20px;
        border-radius: 10px;
        box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
        z-index: 1000;
        font-weight: bold;
        max-width: 400px;
        animation: slideInRight 0.5s ease;
    `;
    successDiv.textContent = message;

    // إضافة الرسالة للصفحة
    document.body.appendChild(successDiv);

    // إزالة الرسالة بعد 4 ثوان
    setTimeout(() => {
        successDiv.style.animation = 'slideOutRight 0.5s ease';
        setTimeout(() => {
            if (successDiv.parentNode) {
                successDiv.parentNode.removeChild(successDiv);
            }
        }, 500);
    }, 4000);
}

// وظيفة اختبار توليد الفيديو
async function testVideoCreation() {
    console.log('🧪 بدء اختبار توليد الفيديو...');

    try {
        // التأكد من وجود آية مختارة
        if (selectedVerses.length === 0) {
            alert('❌ لا توجد آيات مختارة. سأختار آية تلقائياً للاختبار...');

            // اختيار آية الكرسي للاختبار
            selectedVerses = [{
                chapter_number: 2,
                verse_number: 255,
                text_uthmani: 'اللَّهُ لَا إِلَٰهَ إِلَّا هُوَ الْحَيُّ الْقَيُّومُ ۚ لَا تَأْخُذُهُ سِنَةٌ وَلَا نَوْمٌ ۚ لَّهُ مَا فِي السَّمَاوَاتِ وَمَا فِي الْأَرْضِ ۗ مَن ذَا الَّذِي يَشْفَعُ عِندَهُ إِلَّا بِإِذْنِهِ ۚ يَعْلَمُ مَا بَيْنَ أَيْدِيهِمْ وَمَا خَلْفَهُمْ ۖ وَلَا يُحِيطُونَ بِشَيْءٍ مِّنْ عِلْمِهِ إِلَّا بِمَا شَاءَ ۚ وَسِعَ كُرْسِيُّهُ السَّمَاوَاتِ وَالْأَرْضَ ۖ وَلَا يَئُودُهُ حِفْظُهُمَا ۚ وَهُوَ الْعَلِيُّ الْعَظِيمُ',
                verse_key: '2:255'
            }];

            updateVersesPreview();
            updateLivePreview();
        }

        // التأكد من وجود قارئ
        const reciterSelect = document.getElementById('reciterSelect');
        if (!reciterSelect.value) {
            alert('❌ لم يتم اختيار قارئ. سأختار قارئ تلقائياً للاختبار...');

            // اختيار أول قارئ متاح
            if (reciterSelect.options.length > 1) {
                reciterSelect.selectedIndex = 1; // أول قارئ (تجاهل الخيار الفارغ)
            } else {
                alert('❌ لا توجد قراء محملة. تأكد من تحميل القراء أولاً.');
                return;
            }
        }

        // إنشاء بيانات الاختبار
        const testData = {
            recitation_id: reciterSelect.value,
            verses: selectedVerses,
            video_orientation: 'landscape',
            text_settings: textSettings
        };

        console.log('🧪 بيانات الاختبار:', testData);

        // اختبار الاتصال بالخادم
        console.log('🔗 اختبار الاتصال بالخادم...');
        const testResponse = await fetch('http://localhost:5000/', {method: 'GET'});

        if (!testResponse.ok) {
            throw new Error('فشل الاتصال بالخادم');
        }

        console.log('✅ الاتصال بالخادم نجح');

        // اختبار endpoint إنشاء الفيديو
        console.log('🎬 اختبار endpoint إنشاء الفيديو...');
        const videoResponse = await fetch('http://localhost:5000/create_quran_video', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(testData)
        });

        console.log('📨 استجابة الخادم:', videoResponse.status, videoResponse.statusText);
        console.log('📨 headers:', Object.fromEntries(videoResponse.headers.entries()));

        if (!videoResponse.ok) {
            const errorText = await videoResponse.text();
            console.error('❌ خطأ من الخادم:', errorText);
            alert(`❌ خطأ في الخادم (${videoResponse.status}): ${errorText}`);
            return;
        }

        // التحقق من نوع المحتوى
        const contentType = videoResponse.headers.get('content-type');
        console.log('📄 نوع المحتوى:', contentType);

        if (contentType && contentType.includes('video')) {
            console.log('✅ تم إنشاء الفيديو بنجاح!');

            // عرض الفيديو
            const blob = await videoResponse.blob();
            console.log('📦 حجم blob:', blob.size, 'bytes');
            console.log('📦 نوع blob:', blob.type);

            const url = window.URL.createObjectURL(blob);
            console.log('🔗 URL تم إنشاؤه:', url);

            showVideoResult(url);

        } else {
            console.log('⚠️ المحتوى المستلم ليس فيديو');
            const responseText = await videoResponse.text();
            console.log('📄 محتوى الاستجابة:', responseText);
            alert('⚠️ الخادم لم يرجع فيديو. راجع Console للتفاصيل.');
        }

    } catch (error) {
        console.error('❌ خطأ في اختبار توليد الفيديو:', error);
        alert(`❌ فشل اختبار توليد الفيديو: ${error.message}`);
    }
}




