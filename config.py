"""
إعدادات مولد فيديوهات القرآن الكريم
"""

import os

class Config:
    """إعدادات التطبيق الأساسية"""
    
    # إعدادات Flask
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'quran-video-generator-secret-key'
    DEBUG = os.environ.get('FLASK_DEBUG', 'True').lower() == 'true'
    
    # مجلدات التطبيق
    UPLOAD_FOLDER = 'uploads'
    MAX_CONTENT_LENGTH = 100 * 1024 * 1024  # 100MB حد أقصى لحجم الملف
    
    # إعدادات APIs القرآن الكريم
    QURAN_API_BASE = "https://api.quran.com/api/v4"
    CHAPTERS_URL = f"{QURAN_API_BASE}/chapters"
    VERSES_URL = f"{QURAN_API_BASE}/quran/verses/uthmani"
    RECITATIONS_URL = f"{QURAN_API_BASE}/resources/recitations"
    AUDIO_URL = "https://verses.quran.com"
    
    # إعدادات الفيديو
    VIDEO_SETTINGS = {
        'codec': 'libx264',
        'preset': 'ultrafast',
        'audio_codec': 'aac',
        'audio_bitrate': '192k',
        'pixel_format': 'yuv420p'
    }
    
    # إعدادات الصورة
    IMAGE_SETTINGS = {
        'width': 800,
        'height': 600,
        'background_color': 'white',
        'text_color': 'black',
        'font_size': 40
    }
    
    # إعدادات التنظيف
    CLEANUP_DELAY = 10  # ثواني قبل حذف الملفات المؤقتة
    
    # قائمة القراء المفضلين (يمكن تخصيصها)
    PREFERRED_RECITERS = [
        7,   # مشاري راشد العفاسي
        1,   # عبد الباسط عبد الصمد
        3,   # عبد الرحمن السديس
        2,   # عبد الله بصفر
        5    # محمد صديق المنشاوي
    ]

class DevelopmentConfig(Config):
    """إعدادات التطوير"""
    DEBUG = True
    TESTING = False

class ProductionConfig(Config):
    """إعدادات الإنتاج"""
    DEBUG = False
    TESTING = False
    
    # إعدادات أمان إضافية للإنتاج
    SESSION_COOKIE_SECURE = True
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'

class TestingConfig(Config):
    """إعدادات الاختبار"""
    TESTING = True
    DEBUG = True
    WTF_CSRF_ENABLED = False

# اختيار التكوين حسب البيئة
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}
