<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار سريع - مولد فيديوهات القرآن</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            direction: rtl;
            text-align: right;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 16px;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        button:hover:not(:disabled) {
            background: #0056b3;
        }
        .status {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid #007bff;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border-left-color: #dc3545;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border-left-color: #28a745;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 اختبار سريع - مولد فيديوهات القرآن</h1>
        
        <div class="status">
            <h3>خطوات الاختبار:</h3>
            <ol>
                <li>تأكد من تشغيل الخادم: <code>python backend/app.py</code></li>
                <li>اضغط "اختبار الاتصال" للتحقق من الخادم</li>
                <li>اضغط "تحميل البيانات" لجلب السور والقراء</li>
                <li>اختر سورة وآيات وقارئ</li>
                <li>اضغط "اختبار الزر" للتحقق من حالة الزر</li>
            </ol>
        </div>

        <div>
            <button onclick="testConnection()">🔗 اختبار الاتصال</button>
            <button onclick="loadData()">📥 تحميل البيانات</button>
            <button onclick="testButton()">🔍 اختبار الزر</button>
        </div>

        <div style="margin: 20px 0;">
            <label>السورة:</label>
            <select id="chapterSelect" onchange="loadVerses()">
                <option value="">اختر السورة...</option>
            </select>
        </div>

        <div id="versesSection" style="display:none;">
            <label>الآيات:</label>
            <div id="versesContainer" style="max-height: 200px; overflow-y: auto; border: 1px solid #ddd; padding: 10px;">
                <p>جاري تحميل الآيات...</p>
            </div>
        </div>

        <div style="margin: 20px 0;">
            <label>القارئ:</label>
            <select id="reciterSelect" onchange="updateButton()">
                <option value="">اختر القارئ...</option>
            </select>
        </div>

        <div>
            <button id="createBtn" onclick="createVideo()" disabled>
                🎬 توليد الفيديو
            </button>
        </div>

        <div id="status" class="status" style="display:none;"></div>
    </div>

    <script>
        let chapters = [];
        let verses = [];
        let recitations = [];
        let selectedVerses = [];

        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${type}`;
            statusDiv.innerHTML = message;
            statusDiv.style.display = 'block';
        }

        async function testConnection() {
            try {
                const response = await fetch('http://localhost:5000/api/chapters');
                if (response.ok) {
                    showStatus('✅ الاتصال بالخادم ناجح!', 'success');
                } else {
                    showStatus('❌ خطأ في الخادم: ' + response.status, 'error');
                }
            } catch (error) {
                showStatus('❌ لا يمكن الاتصال بالخادم. تأكد من تشغيله على المنفذ 5000', 'error');
            }
        }

        async function loadData() {
            try {
                // تحميل السور
                const chaptersResponse = await fetch('http://localhost:5000/api/chapters');
                chapters = await chaptersResponse.json();
                
                const chapterSelect = document.getElementById('chapterSelect');
                chapterSelect.innerHTML = '<option value="">اختر السورة...</option>';
                chapters.forEach(chapter => {
                    const option = document.createElement('option');
                    option.value = chapter.id;
                    option.textContent = `${chapter.id}. ${chapter.name_arabic}`;
                    chapterSelect.appendChild(option);
                });

                // تحميل القراء
                const recitersResponse = await fetch('http://localhost:5000/api/recitations');
                recitations = await recitersResponse.json();
                
                const reciterSelect = document.getElementById('reciterSelect');
                reciterSelect.innerHTML = '<option value="">اختر القارئ...</option>';
                recitations.forEach(recitation => {
                    const option = document.createElement('option');
                    option.value = recitation.id;
                    option.textContent = recitation.reciter_name;
                    reciterSelect.appendChild(option);
                });

                showStatus(`✅ تم تحميل ${chapters.length} سورة و ${recitations.length} قارئ`, 'success');
            } catch (error) {
                showStatus('❌ خطأ في تحميل البيانات: ' + error.message, 'error');
            }
        }

        async function loadVerses() {
            const chapterSelect = document.getElementById('chapterSelect');
            const chapterId = chapterSelect.value;
            
            if (!chapterId) {
                document.getElementById('versesSection').style.display = 'none';
                return;
            }

            try {
                const response = await fetch(`http://localhost:5000/api/verses/${chapterId}`);
                verses = await response.json();
                
                const versesContainer = document.getElementById('versesContainer');
                versesContainer.innerHTML = '';
                
                verses.slice(0, 5).forEach(verse => { // عرض أول 5 آيات فقط للاختبار
                    const div = document.createElement('div');
                    div.innerHTML = `
                        <label style="display: block; margin: 5px 0;">
                            <input type="checkbox" onchange="updateSelectedVerses()" data-verse="${verse.verse_number}">
                            (${verse.verse_number}) ${verse.text_uthmani.substring(0, 50)}...
                        </label>
                    `;
                    versesContainer.appendChild(div);
                });

                document.getElementById('versesSection').style.display = 'block';
                showStatus(`✅ تم تحميل ${verses.length} آية`, 'success');
            } catch (error) {
                showStatus('❌ خطأ في تحميل الآيات: ' + error.message, 'error');
            }
        }

        function updateSelectedVerses() {
            selectedVerses = [];
            const checkboxes = document.querySelectorAll('#versesContainer input[type="checkbox"]:checked');
            
            checkboxes.forEach(checkbox => {
                const verseNumber = parseInt(checkbox.dataset.verse);
                const verse = verses.find(v => v.verse_number === verseNumber);
                if (verse) {
                    selectedVerses.push(verse);
                }
            });
            
            updateButton();
        }

        function updateButton() {
            const createBtn = document.getElementById('createBtn');
            const reciterSelect = document.getElementById('reciterSelect');
            
            const hasVerses = selectedVerses.length > 0;
            const hasReciter = reciterSelect.value !== '';
            
            createBtn.disabled = !(hasVerses && hasReciter);
            
            if (!hasVerses) {
                createBtn.textContent = '🎬 اختر آيات أولاً';
            } else if (!hasReciter) {
                createBtn.textContent = '🎬 اختر قارئ أولاً';
            } else {
                createBtn.textContent = '🎬 توليد الفيديو';
            }
        }

        function testButton() {
            const createBtn = document.getElementById('createBtn');
            const reciterSelect = document.getElementById('reciterSelect');
            
            const status = {
                'عدد الآيات المختارة': selectedVerses.length,
                'القارئ المختار': reciterSelect.value,
                'حالة الزر': createBtn.disabled ? 'معطل' : 'مفعل',
                'نص الزر': createBtn.textContent
            };
            
            let message = '🔍 حالة الزر:\n\n';
            for (const [key, value] of Object.entries(status)) {
                message += `${key}: ${value}\n`;
            }
            
            alert(message);
        }

        async function createVideo() {
            const reciterSelect = document.getElementById('reciterSelect');
            
            if (selectedVerses.length === 0 || !reciterSelect.value) {
                showStatus('❌ يرجى اختيار آيات وقارئ', 'error');
                return;
            }

            showStatus('🔄 جاري إنشاء الفيديو...', 'info');

            try {
                const requestData = {
                    recitation_id: reciterSelect.value,
                    verses: selectedVerses,
                    custom_image: null
                };

                const response = await fetch('http://localhost:5000/create_quran_video', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestData)
                });

                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.error || 'فشل في إنشاء الفيديو');
                }

                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                
                const link = document.createElement('a');
                link.href = url;
                link.download = 'quran_video.mp4';
                link.click();

                showStatus('✅ تم إنشاء الفيديو بنجاح!', 'success');

            } catch (error) {
                showStatus('❌ خطأ في إنشاء الفيديو: ' + error.message, 'error');
            }
        }
    </script>
</body>
</html>
