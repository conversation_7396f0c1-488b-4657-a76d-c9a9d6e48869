* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: '<PERSON><PERSON>', 'Aria<PERSON>', sans-serif;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 25%, #0f4c75 50%, #3282b8 75%, #bbe1fa 100%);
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
    min-height: 100vh;
    margin: 0;
    padding: 0;
    direction: rtl;
    text-align: right;
    position: relative;
}

/* تأثير الخلفية المتحركة */
@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* إضافة نمط إسلامي للخلفية */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 25% 25%, rgba(255, 215, 0, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(0, 150, 136, 0.1) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
}

/* الشريط العلوي */
.top-header {
    background: linear-gradient(135deg, #0f4c75 0%, #3282b8 50%, #0f4c75 100%);
    background-size: 200% 200%;
    animation: headerGradient 8s ease infinite;
    color: white;
    text-align: center;
    padding: 20px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    position: sticky;
    top: 0;
    z-index: 100;
}

.top-header h1 {
    font-size: 2.2em;
    margin-bottom: 8px;
    font-family: 'Scheherazade New', serif;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.top-header p {
    font-size: 1.1em;
    opacity: 0.9;
}

/* التخطيط الرئيسي */
.main-layout {
    display: flex;
    min-height: calc(100vh - 120px);
    gap: 20px;
    padding: 20px;
    max-width: 1600px;
    margin: 0 auto;
}

/* لوحة التحكم (الجانب الأيسر) */
.controls-panel {
    flex: 1;
    min-width: 400px;
    max-width: 600px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 215, 0, 0.3);
    padding: 20px;
    height: fit-content;
    overflow-y: auto;
    max-height: calc(100vh - 160px);
}

/* لوحة المعاينة (الجانب الأيمن) */
.preview-panel {
    flex: 1;
    min-width: 400px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 215, 0, 0.3);
    padding: 20px;
    height: fit-content;
    position: sticky;
    top: 140px;
    max-height: calc(100vh - 160px);
    overflow-y: auto;
}

/* أقسام التحكم */
.control-section {
    background: rgba(248, 249, 250, 0.8);
    border: 1px solid rgba(255, 215, 0, 0.2);
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    transition: all 0.3s ease;
}

.control-section:hover {
    border-color: rgba(255, 215, 0, 0.4);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.control-section h3 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-family: 'Scheherazade New', 'Amiri', serif;
    font-size: 1.2em;
    border-bottom: 2px solid rgba(255, 215, 0, 0.3);
    padding-bottom: 8px;
}

/* أقسام المعاينة */
.preview-section {
    background: rgba(248, 249, 250, 0.8);
    border: 1px solid rgba(255, 215, 0, 0.2);
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    text-align: center;
}

.preview-section h3 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-family: 'Scheherazade New', 'Amiri', serif;
    font-size: 1.2em;
}

.container {
    max-width: 1000px;
    margin: 0 auto;
    background: rgba(255, 255, 255, 0.98);
    border-radius: 20px;
    box-shadow:
        0 25px 50px rgba(0, 0, 0, 0.15),
        0 0 0 1px rgba(255, 215, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.6);
    overflow: hidden;
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 215, 0, 0.3);
    position: relative;
}

/* إضافة زخرفة إسلامية للحاوية */
.container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #FFD700, #FFA500, #FF8C00, #FFA500, #FFD700);
    background-size: 200% 100%;
    animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
    0%, 100% { background-position: -200% 0; }
    50% { background-position: 200% 0; }
}

header {
    background: linear-gradient(135deg, #0f4c75 0%, #3282b8 50%, #0f4c75 100%);
    background-size: 200% 200%;
    animation: headerGradient 8s ease infinite;
    color: white;
    padding: 40px 30px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

/* تأثير متحرك للهيدر */
@keyframes headerGradient {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

/* إضافة نمط إسلامي للهيدر */
header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 20% 20%, rgba(255, 215, 0, 0.1) 0%, transparent 40%),
        radial-gradient(circle at 80% 80%, rgba(255, 215, 0, 0.1) 0%, transparent 40%),
        radial-gradient(circle at 50% 50%, rgba(255, 255, 255, 0.05) 0%, transparent 60%);
    pointer-events: none;
}

header h1 {
    font-size: 2.8em;
    margin-bottom: 15px;
    font-weight: 700;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    position: relative;
    z-index: 1;
    font-family: 'Scheherazade New', 'Amiri', serif;
}

header p {
    font-size: 1.3em;
    opacity: 0.95;
    position: relative;
    z-index: 1;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
    font-weight: 400;
}

.tabs {
    display: flex;
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.tab-button {
    flex: 1;
    padding: 15px 20px;
    background: transparent;
    border: none;
    cursor: pointer;
    font-size: 16px;
    font-weight: 600;
    color: #6c757d;
    transition: all 0.3s ease;
}

.tab-button.active {
    background: white;
    color: #2c3e50;
    border-bottom: 3px solid #667eea;
}

.tab-button:hover {
    background: #e9ecef;
}

.tab-content {
    display: none;
    padding: 30px;
}

.tab-content.active {
    display: block;
}

.form-section {
    max-width: 600px;
    margin: 0 auto;
}

.input-group {
    margin-bottom: 25px;
}

.input-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #2c3e50;
    font-size: 16px;
}

.input-group select,
.input-group input[type="file"] {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s ease;
    background: white;
}

.input-group select:focus,
.input-group input[type="file"]:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.input-group small {
    display: block;
    margin-top: 5px;
    color: #6c757d;
    font-size: 14px;
}

/* تحسين خيار اتجاه الفيديو */
#videoOrientation {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 2px solid rgba(255, 215, 0, 0.3) !important;
    font-weight: 500;
    font-family: 'Amiri', Arial, sans-serif;
}

#videoOrientation:focus {
    border-color: rgba(255, 215, 0, 0.6) !important;
    box-shadow: 0 0 0 3px rgba(255, 215, 0, 0.1) !important;
}

#videoOrientation option {
    padding: 10px;
    font-size: 14px;
    background: white;
    color: #2c3e50;
    font-weight: 500;
}

/* تصميم المعاينة المباشرة */
#livePreview {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 2px solid rgba(255, 215, 0, 0.3);
    border-radius: 15px;
    padding: 20px;
    margin: 20px 0;
    text-align: center;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

#livePreview h3 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-family: 'Scheherazade New', 'Amiri', serif;
    font-size: 1.3em;
}

#previewContainer {
    position: relative;
    display: inline-block;
}

#previewCanvas {
    max-width: 100%;
    max-height: 600px;
    min-height: 300px;
    border: 3px solid rgba(255, 215, 0, 0.4);
    border-radius: 12px;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
    transition: transform 0.3s ease;
    cursor: pointer;
}

#previewCanvas:hover {
    transform: scale(1.05);
    border-color: rgba(255, 215, 0, 0.6);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

#previewInfo {
    margin-top: 10px;
    font-size: 13px;
    color: #6c757d;
    font-weight: 500;
    background: rgba(255, 255, 255, 0.8);
    padding: 8px 12px;
    border-radius: 8px;
    display: inline-block;
}

#versesContainer {
    max-height: 300px;
    overflow-y: auto;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    background: #f8f9fa;
}

.verse-item {
    display: flex;
    align-items: center;
    padding: 10px;
    margin-bottom: 10px;
    background: white;
    border-radius: 6px;
    border: 1px solid #dee2e6;
    transition: all 0.2s ease;
}

.verse-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.verse-item input[type="checkbox"] {
    margin-left: 10px;
    transform: scale(1.2);
}

.verse-text {
    font-size: 18px;
    line-height: 1.8;
    color: #2c3e50;
}

.verses-preview {
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
}

.verses-preview h4 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 18px;
}

#selectedVersesText {
    font-size: 20px;
    line-height: 2;
    color: #2c3e50;
    text-align: center;
    padding: 15px;
    background: white;
    border-radius: 6px;
    border: 1px solid #dee2e6;
}

button {
    background: linear-gradient(135deg, #0f4c75 0%, #3282b8 50%, #0f4c75 100%);
    background-size: 200% 200%;
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.4s ease;
    width: 100%;
    margin-top: 20px;
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(15, 76, 117, 0.3);
    border: 2px solid rgba(255, 215, 0, 0.2);
}

/* تأثير لامع للأزرار */
button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

button:hover:not(:disabled) {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(15, 76, 117, 0.4);
    background-position: 100% 0;
    border-color: rgba(255, 215, 0, 0.4);
}

button:hover:not(:disabled)::before {
    left: 100%;
}

button:active:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(15, 76, 117, 0.3);
}

button:disabled {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
    cursor: not-allowed;
    transform: none;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-color: rgba(108, 117, 125, 0.2);
}

button:disabled::before {
    display: none;
}

/* تحسين الأزرار الصغيرة */
button[style*="font-size: 10px"] {
    padding: 8px 12px;
    font-size: 11px !important;
    border-radius: 8px;
    margin: 2px;
}

#resultsSection {
    text-align: center;
    padding: 30px;
    background: #f8f9fa;
    margin-top: 20px;
    border-radius: 8px;
}

#resultsSection h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    font-size: 24px;
}

#videoPreview {
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    margin: 20px 0;
}

.download-btn {
    display: inline-block;
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    text-decoration: none;
    padding: 15px 30px;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
    margin-top: 15px;
}

.download-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
}

.error-message {
    background: #f8d7da;
    color: #721c24;
    padding: 15px;
    border-radius: 8px;
    border: 1px solid #f5c6cb;
    margin: 20px 0;
    text-align: center;
}

/* تصميم شريط التقدم المحسن */
.loading-container {
    text-align: center;
    padding: 30px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 15px;
    border: 2px solid rgba(255, 215, 0, 0.2);
    margin: 20px 0;
}

.loading-icon {
    font-size: 4em;
    margin-bottom: 20px;
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

.loading-container h3 {
    color: #2c3e50;
    font-size: 1.5em;
    margin-bottom: 25px;
    font-family: 'Scheherazade New', 'Amiri', serif;
}

.progress-container {
    margin: 25px 0;
    position: relative;
}

.progress-bar {
    width: 100%;
    height: 12px;
    background: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
    position: relative;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #FFD700, #FFA500, #FF8C00);
    border-radius: 10px;
    width: 0%;
    transition: width 0.5s ease;
    position: relative;
    overflow: hidden;
}

.progress-fill::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: shimmerProgress 2s infinite;
}

@keyframes shimmerProgress {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.progress-text {
    margin-top: 10px;
    font-weight: 600;
    color: #2c3e50;
    font-size: 1.1em;
}

.loading-steps {
    margin: 25px 0;
    text-align: right;
}

.step {
    padding: 8px 15px;
    margin: 5px 0;
    background: rgba(255, 255, 255, 0.7);
    border-radius: 8px;
    border-right: 4px solid #e9ecef;
    color: #6c757d;
    transition: all 0.3s ease;
    font-size: 14px;
}

.step.active {
    background: rgba(255, 215, 0, 0.1);
    border-right-color: #FFD700;
    color: #2c3e50;
    font-weight: 600;
    transform: translateX(-5px);
}

.step.completed {
    background: rgba(40, 167, 69, 0.1);
    border-right-color: #28a745;
    color: #155724;
}

.loading-subtitle {
    color: #6c757d;
    font-size: 14px;
    margin-top: 20px;
    font-style: italic;
}

/* تصميم معاينة الصورة المخصصة */
#imagePreview {
    background: rgba(248, 249, 250, 0.8);
    border: 2px dashed rgba(255, 215, 0, 0.3);
    border-radius: 12px;
    padding: 15px;
    text-align: center;
    transition: all 0.3s ease;
}

#imagePreview:hover {
    border-color: rgba(255, 215, 0, 0.5);
    background: rgba(248, 249, 250, 0.9);
}

#previewImg {
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

#previewImg:hover {
    transform: scale(1.05);
}

#imageInfo {
    color: #6c757d;
    font-size: 13px;
    margin-top: 8px;
    font-weight: 500;
}

#removeImageBtn {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%) !important;
    border: none !important;
    padding: 6px 12px !important;
    font-size: 12px !important;
    border-radius: 6px !important;
    margin-top: 8px !important;
    width: auto !important;
    transition: all 0.3s ease !important;
}

#removeImageBtn:hover {
    background: linear-gradient(135deg, #c82333 0%, #a71e2a 100%) !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3) !important;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .container {
        margin: 10px;
        border-radius: 10px;
    }

    header {
        padding: 20px;
    }

    header h1 {
        font-size: 2em;
    }

    .tab-content {
        padding: 20px;
    }

    .tabs {
        flex-direction: column;
    }

    .tab-button {
        padding: 12px;
    }

    /* تحسين المعاينة للشاشات الصغيرة */
    #livePreview {
        padding: 15px;
        margin: 15px 0;
    }

    #previewCanvas {
        max-height: 400px;
        min-height: 200px;
    }

    #previewInfo {
        font-size: 11px;
        padding: 6px 10px;
    }
}

/* تحسينات للتخطيط الجديد */
.debug-section {
    background: rgba(255, 243, 205, 0.8) !important;
    border-color: rgba(255, 193, 7, 0.3) !important;
}

.debug-buttons {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
    justify-content: center;
}

.debug-btn {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%) !important;
    color: #212529 !important;
    border: none !important;
    padding: 8px 12px !important;
    border-radius: 6px !important;
    font-size: 12px !important;
    font-weight: 600 !important;
    width: auto !important;
    margin: 0 !important;
    flex: 1;
    min-width: 80px;
    transition: all 0.3s ease !important;
}

.debug-btn:hover {
    background: linear-gradient(135deg, #fd7e14 0%, #dc3545 100%) !important;
    transform: translateY(-1px) !important;
}

.verses-text {
    font-family: 'Amiri', 'Arial', sans-serif;
    font-size: 18px;
    line-height: 1.8;
    color: #2c3e50;
    text-align: center;
    padding: 15px;
    background: white;
    border-radius: 8px;
    border: 1px solid #dee2e6;
    max-height: 200px;
    overflow-y: auto;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 1200px) {
    .main-layout {
        flex-direction: column;
        gap: 15px;
        padding: 15px;
    }

    .controls-panel,
    .preview-panel {
        min-width: auto;
        max-width: none;
        position: static;
        max-height: none;
    }

    .preview-panel {
        order: -1; /* المعاينة في الأعلى */
    }

    .customization-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 12px;
    }
}

@media (max-width: 768px) {
    .top-header {
        padding: 15px;
    }

    .top-header h1 {
        font-size: 1.8em;
    }

    .top-header p {
        font-size: 1em;
    }

    .main-layout {
        padding: 10px;
        gap: 10px;
    }

    .controls-panel,
    .preview-panel {
        padding: 15px;
        border-radius: 15px;
    }

    .control-section,
    .preview-section {
        padding: 15px;
        margin-bottom: 15px;
    }

    .customization-grid {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .debug-buttons {
        flex-direction: column;
        gap: 5px;
    }

    .debug-btn {
        min-width: auto;
        width: 100% !important;
    }

    #previewCanvas {
        max-height: 300px;
        min-height: 200px;
    }
}

/* زر الآية العشوائية */
.random-verse-btn {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 50%, #ff6b6b 100%) !important;
    color: white !important;
    border: none !important;
    padding: 12px 20px !important;
    border-radius: 25px !important;
    font-size: 16px !important;
    font-weight: bold !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    width: 100% !important;
    margin: 10px 0 !important;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3) !important;
    position: relative !important;
    overflow: hidden !important;
}

.random-verse-btn:hover {
    background: linear-gradient(135deg, #ee5a24 0%, #ff6b6b 50%, #ee5a24 100%) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4) !important;
}

.random-verse-btn:active {
    transform: translateY(0) !important;
    box-shadow: 0 2px 10px rgba(255, 107, 107, 0.3) !important;
}

.random-verse-btn:before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.random-verse-btn:hover:before {
    left: 100%;
}

.random-verse-hint {
    color: #666 !important;
    font-size: 12px !important;
    margin-top: 5px !important;
    text-align: center !important;
    font-style: italic !important;
}

/* رسائل النجاح المتحركة */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

.success-message {
    font-family: 'Amiri', Arial, sans-serif;
    direction: rtl;
    text-align: right;
}

/* تصميم أدوات تخصيص النص */
#textCustomization {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 2px solid rgba(255, 215, 0, 0.3);
    border-radius: 15px;
    padding: 20px;
    margin: 20px 0;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

#textCustomization h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    font-family: 'Scheherazade New', 'Amiri', serif;
    font-size: 1.3em;
    text-align: center;
}

.customization-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 15px;
    max-width: 1000px;
    margin: 0 auto;
}

.custom-group {
    background: rgba(255, 255, 255, 0.8);
    padding: 15px;
    border-radius: 10px;
    border: 1px solid rgba(255, 215, 0, 0.2);
    transition: all 0.3s ease;
}

.custom-group:hover {
    border-color: rgba(255, 215, 0, 0.4);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.custom-group label {
    display: block;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 8px;
    font-size: 14px;
}

.custom-group input[type="range"] {
    width: 100%;
    margin: 8px 0;
    height: 6px;
    border-radius: 3px;
    background: linear-gradient(to right, #0f4c75, #3282b8);
    outline: none;
    -webkit-appearance: none;
}

.custom-group input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: #FFD700;
    cursor: pointer;
    border: 2px solid #0f4c75;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

.custom-group input[type="range"]::-moz-range-thumb {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: #FFD700;
    cursor: pointer;
    border: 2px solid #0f4c75;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

.custom-group input[type="color"] {
    width: 60px;
    height: 40px;
    border: 2px solid #ddd;
    border-radius: 8px;
    cursor: pointer;
    margin-right: 10px;
}

.custom-group select {
    width: 100%;
    padding: 8px 12px;
    border: 2px solid #ddd;
    border-radius: 6px;
    background: white;
    font-size: 14px;
    color: #2c3e50;
}

.custom-group span {
    display: inline-block;
    min-width: 50px;
    font-weight: 600;
    color: #0f4c75;
    font-size: 13px;
}

.preset-btn {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%) !important;
    color: white !important;
    border: none !important;
    padding: 6px 12px !important;
    border-radius: 6px !important;
    font-size: 12px !important;
    margin: 2px !important;
    width: auto !important;
    cursor: pointer;
    transition: all 0.3s ease !important;
}

.preset-btn:hover {
    background: linear-gradient(135deg, #495057 0%, #343a40 100%) !important;
    transform: translateY(-1px) !important;
}

.presets-group {
    grid-column: 1 / -1;
}

.presets-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    justify-content: center;
    margin-top: 10px;
}

.preset-style-btn {
    background: linear-gradient(135deg, #0f4c75 0%, #3282b8 100%) !important;
    color: white !important;
    border: none !important;
    padding: 10px 15px !important;
    border-radius: 8px !important;
    font-size: 13px !important;
    font-weight: 600 !important;
    width: auto !important;
    cursor: pointer;
    transition: all 0.3s ease !important;
    flex: 1;
    min-width: 120px;
}

.preset-style-btn:hover {
    background: linear-gradient(135deg, #3282b8 0%, #0f4c75 100%) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(15, 76, 117, 0.3) !important;
}

.reset-btn {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%) !important;
    color: white !important;
    border: none !important;
    padding: 10px 20px !important;
    border-radius: 8px !important;
    font-size: 14px !important;
    font-weight: 600 !important;
    width: 100% !important;
    cursor: pointer;
    transition: all 0.3s ease !important;
}

.reset-btn:hover {
    background: linear-gradient(135deg, #c82333 0%, #a71e2a 100%) !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3) !important;
}
