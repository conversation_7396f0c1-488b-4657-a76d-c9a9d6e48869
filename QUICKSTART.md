# 🚀 دليل التشغيل السريع

## 📋 المتطلبات الأساسية

1. **Python 3.7+** مثبت على النظام
2. **ffmpeg** مثبت ومتاح في PATH
3. **اتصال بالإنترنت** لجلب بيانات القرآن

## ⚡ التشغيل السريع (3 خطوات)

### 1️⃣ تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 2️⃣ تشغيل الخادم
```bash
python run.py
```

### 3️⃣ فتح الواجهة
سيتم فتح المتصفح تلقائياً، أو اذهب إلى:
```
file:///path/to/frontend/index.html
```

## 🔧 التشغيل اليدوي

### تشغيل الخادم فقط:
```bash
cd backend
python app.py
```

### فتح الواجهة في خادم محلي:
```bash
cd frontend
python -m http.server 8000
```
ثم اذهب إلى: `http://localhost:8000`

## 🧪 اختبار التطبيق

```bash
python test_app.py
```

## 📱 كيفية الاستخدام

### إنشاء فيديو من القرآن:
1. اختر السورة من القائمة
2. اختر الآيات المطلوبة
3. اختر القارئ
4. اضغط "توليد الفيديو"
5. انتظر حتى اكتمال المعالجة
6. حمل الفيديو

### رفع ملفات مخصصة:
1. انتقل لتبويب "رفع ملفات مخصصة"
2. اختر صورة وملف صوتي
3. اضغط "إنشاء الفيديو"

## ⚠️ استكشاف الأخطاء

### خطأ "ffmpeg not found":
- **Windows**: حمل ffmpeg وأضف مجلد bin للـ PATH
- **Mac**: `brew install ffmpeg`
- **Linux**: `sudo apt install ffmpeg`

### خطأ في المكتبات:
```bash
pip install --upgrade -r requirements.txt
```

### خطأ في الاتصال بـ APIs:
- تحقق من اتصال الإنترنت
- جرب مرة أخرى بعد قليل

## 📞 الدعم

إذا واجهت مشاكل:
1. تأكد من تثبيت جميع المتطلبات
2. شغل `python test_app.py` للتشخيص
3. راجع ملف README.md للتفاصيل الكاملة
