#!/usr/bin/env python3
"""
مولد فيديوهات القرآن الكريم
ملف التشغيل السريع
"""

import os
import sys
import subprocess
import webbrowser
from pathlib import Path

def check_requirements():
    """فحص المتطلبات الأساسية"""
    print("🔍 فحص المتطلبات...")
    
    # فحص Python
    if sys.version_info < (3, 7):
        print("❌ يتطلب Python 3.7 أو أحدث")
        return False
    
    # فحص ffmpeg
    try:
        subprocess.run(['ffmpeg', '-version'], 
                      stdout=subprocess.DEVNULL, 
                      stderr=subprocess.DEVNULL, 
                      check=True)
        print("✅ ffmpeg متوفر")
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ ffmpeg غير مثبت أو غير متوفر في PATH")
        print("   يرجى تثبيت ffmpeg من: https://ffmpeg.org/download.html")
        return False
    
    return True

def install_dependencies():
    """تثبيت المتطلبات"""
    print("📦 تثبيت المتطلبات...")
    
    requirements_file = Path("requirements.txt")
    if not requirements_file.exists():
        print("❌ ملف requirements.txt غير موجود")
        return False
    
    try:
        subprocess.run([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'], 
                      check=True)
        print("✅ تم تثبيت المتطلبات بنجاح")
        return True
    except subprocess.CalledProcessError:
        print("❌ فشل في تثبيت المتطلبات")
        return False

def start_server():
    """تشغيل الخادم"""
    print("🚀 تشغيل خادم Flask...")
    
    backend_dir = Path("backend")
    if not backend_dir.exists():
        print("❌ مجلد backend غير موجود")
        return False
    
    app_file = backend_dir / "app.py"
    if not app_file.exists():
        print("❌ ملف app.py غير موجود")
        return False
    
    # تغيير المجلد إلى backend
    os.chdir(backend_dir)
    
    print("✅ الخادم يعمل على: http://localhost:5000")
    print("🌐 افتح frontend/index.html في المتصفح")
    print("⏹️  اضغط Ctrl+C لإيقاف الخادم")
    
    try:
        # فتح المتصفح تلقائياً
        frontend_path = Path("../frontend/index.html").resolve()
        if frontend_path.exists():
            webbrowser.open(f"file://{frontend_path}")
        
        # تشغيل الخادم
        subprocess.run([sys.executable, 'app.py'])
    except KeyboardInterrupt:
        print("\n👋 تم إيقاف الخادم")
    except Exception as e:
        print(f"❌ خطأ في تشغيل الخادم: {e}")

def main():
    """الوظيفة الرئيسية"""
    print("🕌 مولد فيديوهات القرآن الكريم")
    print("=" * 40)
    
    # فحص المتطلبات
    if not check_requirements():
        print("\n❌ يرجى إصلاح المتطلبات أولاً")
        return
    
    # تثبيت المتطلبات
    if not install_dependencies():
        print("\n❌ فشل في تثبيت المتطلبات")
        return
    
    # تشغيل الخادم
    start_server()

if __name__ == "__main__":
    main()
