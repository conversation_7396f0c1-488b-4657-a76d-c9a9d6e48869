#!/usr/bin/env python3
"""
اختبار بسيط لمولد فيديوهات القرآن الكريم
"""

import requests
import json
import sys
import os

def test_server_connection():
    """اختبار الاتصال بالخادم"""
    try:
        response = requests.get('http://localhost:5000/api/chapters', timeout=5)
        if response.status_code == 200:
            print("✅ الخادم يعمل بشكل صحيح")
            return True
        else:
            print(f"❌ خطأ في الخادم: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ لا يمكن الاتصال بالخادم. تأكد من تشغيل الخادم على المنفذ 5000")
        return False
    except Exception as e:
        print(f"❌ خطأ في الاتصال: {e}")
        return False

def test_chapters_api():
    """اختبار API السور"""
    try:
        response = requests.get('http://localhost:5000/api/chapters')
        if response.status_code == 200:
            chapters = response.json()
            print(f"✅ تم جلب {len(chapters)} سورة")
            
            # عرض أول 5 سور كمثال
            print("أول 5 سور:")
            for chapter in chapters[:5]:
                print(f"  {chapter['id']}. {chapter['name_arabic']}")
            return True
        else:
            print(f"❌ فشل في جلب السور: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ خطأ في اختبار السور: {e}")
        return False

def test_verses_api():
    """اختبار API الآيات"""
    try:
        # اختبار جلب آيات سورة الفاتحة (رقم 1)
        response = requests.get('http://localhost:5000/api/verses/1')
        if response.status_code == 200:
            verses = response.json()
            print(f"✅ تم جلب {len(verses)} آية من سورة الفاتحة")
            
            # عرض أول آية كمثال
            if verses:
                first_verse = verses[0]
                print(f"الآية الأولى: {first_verse['text_uthmani']}")
            return True
        else:
            print(f"❌ فشل في جلب الآيات: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ خطأ في اختبار الآيات: {e}")
        return False

def test_recitations_api():
    """اختبار API القراء"""
    try:
        response = requests.get('http://localhost:5000/api/recitations')
        if response.status_code == 200:
            recitations = response.json()
            print(f"✅ تم جلب {len(recitations)} قارئ")
            
            # عرض أول 3 قراء كمثال
            print("أول 3 قراء:")
            for recitation in recitations[:3]:
                print(f"  {recitation['id']}. {recitation['reciter_name']}")
            return True
        else:
            print(f"❌ فشل في جلب القراء: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ خطأ في اختبار القراء: {e}")
        return False

def test_video_creation():
    """اختبار إنشاء فيديو (اختبار أساسي)"""
    try:
        # بيانات اختبار بسيطة
        test_data = {
            "recitation_id": 7,  # مشاري راشد العفاسي
            "verses": [
                {
                    "chapter_number": 1,
                    "verse_number": 1,
                    "text_uthmani": "بِسْمِ ٱللَّهِ ٱلرَّحْمَٰنِ ٱلرَّحِيمِ"
                }
            ],
            "custom_image": None
        }
        
        print("🔄 اختبار إنشاء فيديو (قد يستغرق وقتاً...)...")
        response = requests.post(
            'http://localhost:5000/create_quran_video',
            json=test_data,
            timeout=60  # دقيقة واحدة
        )
        
        if response.status_code == 200:
            print("✅ تم إنشاء الفيديو بنجاح!")
            print(f"حجم الفيديو: {len(response.content)} بايت")
            return True
        else:
            error_data = response.json() if response.headers.get('content-type') == 'application/json' else response.text
            print(f"❌ فشل في إنشاء الفيديو: {error_data}")
            return False
    except requests.exceptions.Timeout:
        print("⏰ انتهت مهلة الاختبار. قد يكون الخادم بطيئاً أو هناك مشكلة في الشبكة")
        return False
    except Exception as e:
        print(f"❌ خطأ في اختبار إنشاء الفيديو: {e}")
        return False

def main():
    """تشغيل جميع الاختبارات"""
    print("🕌 اختبار مولد فيديوهات القرآن الكريم")
    print("=" * 50)
    
    tests = [
        ("اختبار الاتصال بالخادم", test_server_connection),
        ("اختبار API السور", test_chapters_api),
        ("اختبار API الآيات", test_verses_api),
        ("اختبار API القراء", test_recitations_api),
        ("اختبار إنشاء الفيديو", test_video_creation)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 {test_name}...")
        if test_func():
            passed += 1
        else:
            print("   💡 تأكد من تشغيل الخادم: python backend/app.py")
    
    print(f"\n📊 النتائج: {passed}/{total} اختبار نجح")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! التطبيق جاهز للاستخدام")
    else:
        print("⚠️  بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه")
        sys.exit(1)

if __name__ == "__main__":
    main()
