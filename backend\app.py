import os
import sys
import uuid
import subprocess
import requests
import json
from threading import Timer
from flask import Flask, request, send_file, jsonify
from flask_cors import CORS
from PIL import Image, ImageDraw, ImageFont
import arabic_reshaper
from bidi.algorithm import get_display

# إضافة المجلد الجذر للمسار لاستيراد التكوين
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config import Config

app = Flask(__name__)
app.config.from_object(Config)
CORS(app)

# إنشاء مجلد الرفع
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

# URLs من التكوين
QURAN_API_BASE = app.config['QURAN_API_BASE']
CHAPTERS_URL = app.config['CHAPTERS_URL']
VERSES_URL = app.config['VERSES_URL']
RECITATIONS_URL = app.config['RECITATIONS_URL']
AUDIO_URL = app.config['AUDIO_URL']

def remove_file_later(path, delay=None):
    """حذف ملف بعد تأخير محدد"""
    if delay is None:
        delay = app.config.get('CLEANUP_DELAY', 10)

    def remove():
        if os.path.exists(path):
            try:
                os.remove(path)
                print(f"تم حذف الملف: {path}")
            except Exception as e:
                print(f"خطأ في حذف الملف {path}: {e}")

    Timer(delay, remove).start()

@app.route('/', methods=['GET'])
def home():
    """الصفحة الرئيسية للخادم"""
    return jsonify({
        "message": "خادم مولد الفيديو القرآني يعمل بنجاح!",
        "status": "running",
        "endpoints": [
            "/api/chapters",
            "/api/verses/<chapter_number>",
            "/api/recitations",
            "/create_quran_video",
            "/api/test_audio/<recitation_id>/<verse_key>"
        ]
    })

def get_audio_duration(audio_path):
    """استخراج مدة الصوت بالثواني باستخدام ffprobe"""
    try:
        result = subprocess.run([
            'ffprobe', '-v', 'error',
            '-show_entries', 'format=duration',
            '-of', 'default=noprint_wrappers=1:nokey=1',
            audio_path
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        return float(result.stdout.strip())
    except Exception:
        return None

@app.route('/api/chapters', methods=['GET'])
def get_chapters():
    """جلب قائمة السور من API القرآن الكريم"""
    try:
        response = requests.get(CHAPTERS_URL)
        response.raise_for_status()
        data = response.json()
        return jsonify(data['chapters'])
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/api/verses/<int:chapter_number>', methods=['GET'])
def get_verses(chapter_number):
    """جلب آيات سورة معينة"""
    try:
        params = {
            'chapter_number': chapter_number
        }
        response = requests.get(VERSES_URL, params=params)
        response.raise_for_status()
        data = response.json()
        return jsonify(data['verses'])
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/api/recitations', methods=['GET'])
def get_recitations():
    """جلب قائمة القراء"""
    try:
        response = requests.get(RECITATIONS_URL)
        response.raise_for_status()
        data = response.json()
        return jsonify(data['recitations'])
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/api/test_audio/<int:recitation_id>/<path:verse_key>', methods=['GET'])
def test_audio_download(recitation_id, verse_key):
    """اختبار تنزيل الصوت لآية معينة"""
    try:
        print(f"🧪 اختبار تنزيل الصوت: القارئ {recitation_id}, الآية {verse_key}")

        # تحويل verse_key إلى تنسيقات مختلفة
        chapter_num, verse_num = verse_key.split(':')
        verse_num_padded = verse_num.zfill(3)
        chapter_num_padded = chapter_num.zfill(3)

        # خريطة القراء (نفس الخريطة في download_audio)
        reciter_folders = {
            1: "AbdulSamad_64kbps_QuranExplorer.Com",
            2: "Abdullah_Basfar_192kbps",
            3: "abdurrahmaan_as-sudays_64kbps",
            4: "Abu_Bakr_Ash-Shaatree_128kbps",
            5: "Alafasy_128kbps",
            6: "maher_almuaiqly_64kbps",
            7: "Alafasy_128kbps",
            8: "Husary_128kbps",
            9: "Minshawi_Murattal_128kbps",
            10: "Saad_Al-Ghamdi_64kbps",
        }

        # قائمة URLs للاختبار
        test_urls = []

        # إضافة روابط everyayah.com الصحيحة
        if int(recitation_id) in reciter_folders:
            folder_name = reciter_folders[int(recitation_id)]
            test_urls.extend([
                f"https://everyayah.com/data/{folder_name}/{chapter_num_padded}{verse_num_padded}.mp3",
                f"https://www.everyayah.com/data/{folder_name}/{chapter_num_padded}{verse_num_padded}.mp3",
            ])

        # إضافة مصادر أخرى
        test_urls.extend([
            f"https://audio.qurancdn.com/ayah/{recitation_id}/{verse_key}.mp3",
            f"https://verses.quran.com/{recitation_id}/{chapter_num_padded}{verse_num_padded}.mp3",
            f"https://audio.qurancdn.com/{recitation_id}/{chapter_num_padded}{verse_num_padded}.mp3",
            f"https://verses.quran.com/{recitation_id}/{verse_key}.mp3",
            f"https://audio.qurancdn.com/{recitation_id}/{verse_key}.mp3"
        ])

        results = []
        for url in test_urls:
            try:
                response = requests.head(url, timeout=10)  # استخدام HEAD للسرعة
                results.append({
                    "url": url,
                    "status": response.status_code,
                    "accessible": response.status_code == 200
                })
            except Exception as e:
                results.append({
                    "url": url,
                    "status": "error",
                    "error": str(e),
                    "accessible": False
                })

        return jsonify({
            "recitation_id": recitation_id,
            "verse_key": verse_key,
            "test_results": results
        })

    except Exception as e:
        return jsonify({"error": str(e)}), 500

def download_audio(recitation_id, verse_key):
    """تنزيل ملف صوتي لآية معينة"""
    try:
        # تحويل verse_key إلى تنسيقات مختلفة
        chapter_num, verse_num = verse_key.split(':')
        verse_num_padded = verse_num.zfill(3)  # مثل 001, 002
        chapter_num_padded = chapter_num.zfill(3)  # مثل 001, 002

        # خريطة القراء المشهورين (ID -> اسم المجلد في everyayah.com)
        reciter_folders = {
            1: "AbdulSamad_64kbps_QuranExplorer.Com",  # عبد الباسط عبد الصمد
            2: "Abdullah_Basfar_192kbps",              # عبد الله بصفر
            3: "abdurrahmaan_as-sudays_64kbps",       # عبد الرحمن السديس
            4: "Abu_Bakr_Ash-Shaatree_128kbps",       # أبو بكر الشاطري
            5: "Alafasy_128kbps",                     # مشاري راشد العفاسي
            6: "maher_almuaiqly_64kbps",              # ماهر المعيقلي
            7: "Alafasy_128kbps",                     # مشاري راشد العفاسي (نسخة أخرى)
            8: "Husary_128kbps",                      # محمود خليل الحصري
            9: "Minshawi_Murattal_128kbps",           # محمد صديق المنشاوي
            10: "Saad_Al-Ghamdi_64kbps",             # سعد الغامدي
        }

        # قائمة URLs محتملة للتجربة (مرتبة حسب الأولوية)
        possible_urls = []

        # إضافة روابط everyayah.com بأسماء المجلدات الصحيحة
        if int(recitation_id) in reciter_folders:
            folder_name = reciter_folders[int(recitation_id)]
            possible_urls.extend([
                f"https://everyayah.com/data/{folder_name}/{chapter_num_padded}{verse_num_padded}.mp3",
                f"https://www.everyayah.com/data/{folder_name}/{chapter_num_padded}{verse_num_padded}.mp3",
            ])

        # إضافة مصادر أخرى كـ fallback
        possible_urls.extend([
            # مصادر QuranCDN
            f"https://audio.qurancdn.com/ayah/{recitation_id}/{verse_key}.mp3",
            f"https://verses.quran.com/{recitation_id}/{chapter_num_padded}{verse_num_padded}.mp3",
            f"https://audio.qurancdn.com/{recitation_id}/{chapter_num_padded}{verse_num_padded}.mp3",

            # تنسيقات أخرى
            f"https://verses.quran.com/{recitation_id}/{verse_key}.mp3",
            f"https://audio.qurancdn.com/{recitation_id}/{verse_key}.mp3",

            # مصادر إضافية
            f"https://download.quranicaudio.com/quran/{recitation_id}/{chapter_num_padded}{verse_num_padded}.mp3",
            f"https://cdn.islamic.network/quran/audio/{recitation_id}/{verse_key}.mp3",
            f"{AUDIO_URL}/{recitation_id}/{verse_key}.mp3",
        ])

        print(f"🔄 محاولة تنزيل الصوت للآية {verse_key} بالقارئ {recitation_id}")

        for i, audio_url in enumerate(possible_urls):
            try:
                print(f"  المحاولة {i+1}: {audio_url}")

                # محاولة HEAD request أولاً للتحقق من وجود الملف
                head_response = requests.head(audio_url, timeout=10)
                print(f"    HEAD response: {head_response.status_code}")

                if head_response.status_code == 200:
                    content_length = head_response.headers.get('content-length')
                    content_type = head_response.headers.get('content-type')
                    print(f"    Content-Length: {content_length}, Content-Type: {content_type}")

                    # إذا كان الملف موجود، قم بتحميله
                    response = requests.get(audio_url, timeout=30)

                    if response.status_code == 200 and len(response.content) > 1000:  # تأكد من وجود محتوى صوتي
                        filename = f"{uuid.uuid4().hex}_verse_{verse_key.replace(':', '_')}.mp3"
                        filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)

                        with open(filepath, 'wb') as f:
                            f.write(response.content)

                        print(f"  ✅ تم تنزيل الصوت بنجاح: {filepath} ({len(response.content)} bytes)")

                        # التحقق من أن الملف صوتي فعلاً
                        if response.headers.get('content-type', '').startswith('audio/') or len(response.content) > 5000:
                            return filepath
                        else:
                            print(f"  ⚠️ الملف قد لا يكون صوتياً: {response.headers.get('content-type')}")
                            os.remove(filepath)  # حذف الملف غير الصالح
                    else:
                        print(f"  ❌ فشل في التحميل: HTTP {response.status_code}, حجم المحتوى: {len(response.content)}")
                else:
                    print(f"  ❌ الملف غير موجود: HTTP {head_response.status_code}")

            except requests.exceptions.RequestException as e:
                print(f"  ❌ خطأ في الطلب: {e}")
                continue

        print(f"❌ فشل في تنزيل الصوت من جميع المصادر للآية {verse_key}")
        return None

    except Exception as e:
        print(f"❌ خطأ عام في تنزيل الصوت: {e}")
        return None

def create_verse_image(verses_text, width=None, height=None, text_settings=None):
    """إنشاء صورة تحتوي على الآيات"""
    try:

        # استخدام الإعدادات من التكوين كقيم افتراضية
        default_settings = app.config['IMAGE_SETTINGS']
        width = width or default_settings['width']
        height = height or default_settings['height']

        # استخدام إعدادات النص المخصصة إذا تم تمريرها
        if text_settings:
            bg_color = text_settings.get('backgroundColor', default_settings['background_color'])
            text_color = text_settings.get('textColor', default_settings['text_color'])
            font_size = int(text_settings.get('fontSize', default_settings['font_size']))
            text_position = text_settings.get('textPosition', 'bottom')
            text_padding = int(text_settings.get('textPadding', 40))
        else:
            bg_color = default_settings['background_color']
            text_color = default_settings['text_color']
            font_size = default_settings['font_size']
            text_position = 'bottom'
            text_padding = 40

        # إنشاء صورة بخلفية محددة
        img = Image.new('RGB', (width, height), color=bg_color)
        draw = ImageDraw.Draw(img)

        # محاولة استخدام خط عربي يدعم الأرقام العربية الهندية
        try:
            # خطوط تدعم العربية والأرقام العربية الهندية بشكل ممتاز
            # الحصول على نوع الخط المختار من إعدادات النص
            font_family = 'Amiri'  # افتراضي
            if text_settings and 'fontFamily' in text_settings:
                font_family = text_settings['fontFamily']



            # تحديد مسارات الخطوط حسب النوع المختار
            if font_family == 'Uthmanic':
                font_paths = [
                    "fonts/Uthmanic-Regular.otf",            # عثماني محلي
                    "fonts/Uthmanic-Regular.ttf",            # عثماني محلي - نسخة TTF
                    "C:/Windows/Fonts/Uthmanic-Regular.otf", # عثماني نظام
                    "C:/Windows/Fonts/Uthmanic-Regular.ttf", # عثماني نظام - نسخة TTF
                ]
            elif font_family == 'Arial':
                font_paths = [
                    "C:/Windows/Fonts/arial.ttf",            # أريال
                    "arial.ttf",
                    "/System/Library/Fonts/Arial.ttf",      # ماك
                ]
            elif font_family == 'Tahoma':
                font_paths = [
                    "C:/Windows/Fonts/tahoma.ttf",           # تاهوما
                    "tahoma.ttf",
                ]
            else:  # Amiri (افتراضي)
                font_paths = [
                    "fonts/Amiri-Regular.ttf",               # أميري محلي
                    "fonts/amiri.ttf",                       # أميري محلي - اسم بديل
                    "C:/Windows/Fonts/Amiri-Regular.ttf",     # أميري نظام
                    "C:/Windows/Fonts/amiri.ttf",             # أميري نظام - اسم بديل
                ]

            # إضافة خطوط احتياطية
            font_paths.extend([
                "C:/Windows/Fonts/ScheherazadeNew-Regular.ttf", # شهرزاد الجديد
                "C:/Windows/Fonts/NotoSansArabic-Regular.ttf",  # نوتو العربي
                "C:/Windows/Fonts/segoeui.ttf",         # سيجو UI - دعم ممتاز للعربية
                "C:/Windows/Fonts/calibri.ttf",         # كاليبري
                "C:/Windows/Fonts/times.ttf",           # تايمز
                "/System/Library/Fonts/Helvetica.ttf",  # ماك
                "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf", # لينكس
                "/usr/share/fonts/truetype/liberation/LiberationSans-Regular.ttf"
            ])

            font = None
            for font_path in font_paths:
                try:
                    font = ImageFont.truetype(font_path, font_size)
                    # اختبار الخط مع الأرقام العربية
                    test_text = "۝١۝ ۝٢۝ ۝٣۝"
                    test_bbox = draw.textbbox((0, 0), test_text, font=font)
                    print(f"🧪 اختبار الخط مع الأرقام العربية: '{test_text}'")
                    print(f"📏 أبعاد النص الاختباري: {test_bbox}")

                    # اختبار إضافي: رسم النص على صورة صغيرة للتحقق
                    test_img = Image.new('RGB', (200, 50), color='white')
                    test_draw = ImageDraw.Draw(test_img)
                    test_draw.text((10, 10), test_text, font=font, fill='black')
                    test_path = os.path.join(app.config['UPLOAD_FOLDER'], f"font_test_{uuid.uuid4().hex[:8]}.png")
                    test_img.save(test_path)
                    print(f"🧪 حفظ اختبار الخط في: {test_path}")

                    break
                except Exception as e:
                    print(f"❌ فشل تحميل الخط {font_path}: {e}")
                    continue

            if font is None:
                print("⚠️ لم يتم العثور على خط مناسب، استخدام الخط الافتراضي")
                font = ImageFont.load_default()

        except Exception as e:
            print(f"❌ خطأ في تحميل الخط: {e}")
            font = ImageFont.load_default()

        # تحويل الأرقام الإنجليزية إلى العربية الهندية
        arabic_numbers_text = ensure_arabic_verse_numbers(verses_text)

        # تنسيق النص العربي
        reshaped_text = arabic_reshaper.reshape(arabic_numbers_text)
        bidi_text = get_display(reshaped_text)

        # تقسيم النص إلى أسطر إذا كان طويلاً
        words = bidi_text.split(' ')
        lines = []
        current_line = ""

        for word in words:
            test_line = current_line + " " + word if current_line else word
            bbox = draw.textbbox((0, 0), test_line, font=font)
            if bbox[2] - bbox[0] < width - 40:  # ترك هامش 20 بكسل من كل جانب
                current_line = test_line
            else:
                if current_line:
                    lines.append(current_line)
                current_line = word

        if current_line:
            lines.append(current_line)

        # عكس ترتيب الأسطر للنص العربي (السطر الأول يجب أن يكون في الأعلى)
        lines.reverse()
        print(f"📝 تم تقسيم النص إلى {len(lines)} سطر وعكس الترتيب للعربية")

        # حساب الارتفاع الإجمالي للنص
        line_height = font_size + 10
        total_height = len(lines) * line_height

        # حساب موقع النص حسب الإعداد المختار
        if text_position == 'top':
            start_y = text_padding
        elif text_position == 'center':
            start_y = (height - total_height) // 2
        elif text_position == 'bottom-center':
            start_y = height - total_height - (text_padding * 2)
        else:  # 'bottom' (افتراضي)
            start_y = height - total_height - text_padding

        print(f"📍 موقع النص المحسوب: start_y = {start_y} (موقع: {text_position})")

        # رسم كل سطر
        for i, line in enumerate(lines):
            bbox = draw.textbbox((0, 0), line, font=font)
            text_width = bbox[2] - bbox[0]
            x = (width - text_width) // 2
            y = start_y + (i * line_height)

            draw.text((x, y), line, font=font, fill=text_color)

        # حفظ الصورة
        filename = f"{uuid.uuid4().hex}_verses.png"
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        img.save(filepath)

        return filepath
    except Exception as e:
        print(f"❌ خطأ في إنشاء صورة الآيات: {e}")
        print(f"❌ تفاصيل الخطأ: {type(e).__name__}")
        import traceback
        print(f"❌ تتبع الخطأ: {traceback.format_exc()}")
        return None

@app.route('/create_quran_video', methods=['POST'])
def create_quran_video():
    """إنشاء فيديو قرآني جديد"""

    try:
        data = request.get_json()
        print(f"📨 البيانات المستلمة: {data}")

        if not data:
            print("❌ لم يتم إرسال بيانات")
            return jsonify({"error": "لم يتم إرسال بيانات"}), 400

        recitation_id = data.get('recitation_id')
        verses = data.get('verses', [])
        custom_image = data.get('custom_image')
        video_orientation = data.get('video_orientation', 'landscape')  # افتراضي: أفقي
        text_settings = data.get('text_settings', {})  # إعدادات النص المخصصة

        if not recitation_id or not verses:
            return jsonify({"error": "يرجى تحديد القارئ والآيات"}), 400

        # تنزيل ملفات الصوت للآيات المختارة
        audio_files = []
        verses_text = ""

        print(f"📥 بدء تنزيل الصوت لـ {len(verses)} آية(آيات)")

        for i, verse in enumerate(verses):
            verse_key = f"{verse['chapter_number']}:{verse['verse_number']}"
            print(f"  📥 تنزيل الآية {i+1}/{len(verses)}: {verse_key}")

            print(f"    🔄 محاولة تنزيل الصوت...")
            audio_path = download_audio(recitation_id, verse_key)

            if audio_path:
                audio_files.append(audio_path)
                print(f"    ✅ تم تنزيل: {audio_path}")

                # التحقق من حجم الملف
                file_size = os.path.getsize(audio_path)
                print(f"    📊 حجم الملف: {file_size} bytes")

                if file_size < 1000:
                    print(f"    ⚠️ تحذير: الملف صغير جداً، قد يكون تالف")
                else:
                    print(f"    ✅ الملف يبدو صالحاً")
            else:
                print(f"    ❌ فشل في تنزيل الآية: {verse_key}")
                print(f"    🔍 جرب اختبار الروابط يدوياً للقارئ {recitation_id}")

            verses_text += verse['text_uthmani'] + " "

        print(f"📊 إجمالي الملفات المحملة: {len(audio_files)} من {len(verses)}")

        if not audio_files:
            print("❌ فشل في تنزيل جميع ملفات الصوت!")
            print("🔍 تفاصيل المحاولات:")
            for i, verse in enumerate(verses):
                verse_key = f"{verse['chapter_number']}:{verse['verse_number']}"
                print(f"  الآية {i+1}: {verse_key} - فشل التنزيل")

            return jsonify({
                "error": "فشل في تنزيل ملفات الصوت. يرجى المحاولة مرة أخرى أو اختيار قارئ آخر.",
                "details": "تم اختبار جميع مصادر الصوت المتاحة ولم يتم العثور على ملفات صوتية صالحة."
            }), 500

        # إنشاء نص موحد من جميع الآيات مع أرقامها
        verses_text = add_verse_numbers_to_text(verses)
        print(f"📝 النص الموحد مع أرقام الآيات: {verses_text[:100]}...")

        # تحديد أبعاد الفيديو حسب الاتجاه المختار
        video_dimensions = get_video_dimensions(video_orientation)
        print(f"📱 اتجاه الفيديو: {video_orientation}")
        print(f"📏 أبعاد الفيديو: {video_dimensions['width']}x{video_dimensions['height']}")

        # طباعة إعدادات النص إذا تم إرسالها
        if text_settings:
            print(f"🎨 إعدادات النص المخصصة: {text_settings}")

        # دمج ملفات الصوت إذا كان هناك أكثر من ملف
        if len(audio_files) > 1:
            print(f"🔗 دمج {len(audio_files)} ملفات صوتية...")

            merged_audio = f"{uuid.uuid4().hex}_merged.mp3"
            merged_path = os.path.join(app.config['UPLOAD_FOLDER'], merged_audio)

            # إنشاء قائمة ملفات للدمج
            file_list = f"{uuid.uuid4().hex}_files.txt"
            file_list_path = os.path.join(app.config['UPLOAD_FOLDER'], file_list)

            with open(file_list_path, 'w', encoding='utf-8') as f:
                for audio_file in audio_files:
                    # استخدام مسار مطلق وتنظيف المسار
                    clean_path = os.path.abspath(audio_file).replace('\\', '/')
                    f.write(f"file '{clean_path}'\n")
                    print(f"    📁 إضافة ملف للدمج: {clean_path}")

            print(f"  📝 قائمة الملفات: {file_list_path}")

            try:
                # دمج الملفات باستخدام ffmpeg
                result = subprocess.run([
                    'ffmpeg', '-y', '-f', 'concat', '-safe', '0',
                    '-i', file_list_path, '-c', 'copy', merged_path
                ], check=True, capture_output=True, text=True)

                print(f"  ✅ تم دمج الصوت: {merged_path}")

                # التحقق من حجم الملف المدموج
                merged_size = os.path.getsize(merged_path)
                print(f"  📊 حجم الملف المدموج: {merged_size} bytes")

                final_audio_path = merged_path

            except subprocess.CalledProcessError as e:
                print(f"  ❌ خطأ في دمج الصوت: {e}")
                print(f"  📄 stdout: {e.stdout}")
                print(f"  📄 stderr: {e.stderr}")
                print(f"  📄 return code: {e.returncode}")

                # التحقق من محتوى ملف القائمة
                try:
                    with open(file_list_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        print(f"  📄 محتوى ملف القائمة:\n{content}")
                except Exception as read_error:
                    print(f"  ❌ خطأ في قراءة ملف القائمة: {read_error}")

                # استخدام الملف الأول كـ fallback
                final_audio_path = audio_files[0]
                print(f"  🔄 استخدام الملف الأول: {final_audio_path}")

                # التحقق من وجود الملف الأول
                if not os.path.exists(final_audio_path):
                    print(f"  ❌ الملف الأول غير موجود: {final_audio_path}")
                    return jsonify({"error": "فشل في دمج الصوت والملف الأول غير موجود"}), 500

            # حذف ملفات الصوت المؤقتة
            for audio_file in audio_files:
                if os.path.exists(audio_file):
                    os.remove(audio_file)
            if os.path.exists(file_list_path):
                os.remove(file_list_path)
        else:
            final_audio_path = audio_files[0]
            print(f"🎵 استخدام ملف صوتي واحد: {final_audio_path}")

        # إنشاء أو استخدام الصورة
        if custom_image and custom_image != 'null':
            # استخدام الصورة المخصصة المرفوعة
            print(f"🖼️ استخدام صورة مخصصة: {custom_image}")

            # التحقق من وجود الصورة المخصصة
            if not os.path.exists(custom_image):
                print(f"❌ الصورة المخصصة غير موجودة: {custom_image}")
                # إنشاء صورة افتراضية كـ fallback
                image_path = create_verse_image(
                    verses_text,
                    video_dimensions['width'],
                    video_dimensions['height'],
                    text_settings
                )
            else:
                # معالجة الصورة المخصصة لضمان التوافق مع ffmpeg
                image_path = process_custom_image_for_video(
                    custom_image,
                    verses_text,
                    video_dimensions['width'],
                    video_dimensions['height'],
                    text_settings
                )
        else:
            # إنشاء صورة تحتوي على الآيات
            print(f"🖼️ إنشاء صورة تحتوي على الآيات")
            image_path = create_verse_image(
                verses_text,
                video_dimensions['width'],
                video_dimensions['height'],
                text_settings
            )

        if not image_path:
            print("❌ فشل في إنشاء الصورة - image_path فارغ")
            return jsonify({"error": "فشل في إنشاء الصورة"}), 500

        # الحصول على مدة الصوت
        duration = get_audio_duration(final_audio_path)
        if duration is None:
            return jsonify({"error": "تعذر استخراج مدة ملف الصوت"}), 500

        # إنشاء الفيديو باستخدام إعدادات التكوين
        output_filename = f"{uuid.uuid4().hex}_quran_video.mp4"
        output_path = os.path.join(app.config['UPLOAD_FOLDER'], output_filename)

        video_settings = app.config['VIDEO_SETTINGS']

        print(f"🎬 إنشاء الفيديو النهائي...")
        print(f"  📸 الصورة: {image_path}")
        print(f"  🎵 الصوت: {final_audio_path}")
        print(f"  ⏱️ المدة: {duration} ثانية")
        print(f"  📹 الفيديو: {output_path}")

        # التحقق من وجود الملفات
        if not os.path.exists(image_path):
            print(f"  ❌ الصورة غير موجودة: {image_path}")
            return jsonify({"error": "ملف الصورة غير موجود"}), 500

        if not os.path.exists(final_audio_path):
            print(f"  ❌ الصوت غير موجود: {final_audio_path}")
            return jsonify({"error": "ملف الصوت غير موجود"}), 500

        # أحجام الملفات
        image_size = os.path.getsize(image_path)
        audio_size = os.path.getsize(final_audio_path)
        print(f"  📊 حجم الصورة: {image_size} bytes")
        print(f"  📊 حجم الصوت: {audio_size} bytes")

        try:
            ffmpeg_cmd = [
                'ffmpeg', '-y',
                '-loop', '1',
                '-i', image_path,
                '-i', final_audio_path,
                '-c:v', video_settings['codec'],
                '-tune', 'stillimage',
                '-preset', video_settings['preset'],
                '-c:a', video_settings['audio_codec'],
                '-b:a', video_settings['audio_bitrate'],
                '-pix_fmt', video_settings['pixel_format'],
                '-t', str(duration),
                '-movflags', '+faststart',
                output_path
            ]

            print(f"  🔧 أمر ffmpeg: {' '.join(ffmpeg_cmd)}")

            result = subprocess.run(ffmpeg_cmd, check=True, capture_output=True, text=True)

            print(f"  ✅ تم إنشاء الفيديو بنجاح!")

            # التحقق من حجم الفيديو النهائي
            if os.path.exists(output_path):
                video_size = os.path.getsize(output_path)
                print(f"  📊 حجم الفيديو النهائي: {video_size} bytes")

                if video_size < 10000:
                    print(f"  ⚠️ تحذير: الفيديو صغير جداً، قد يكون هناك مشكلة")
            else:
                print(f"  ❌ الفيديو لم يتم إنشاؤه!")
                return jsonify({"error": "فشل في إنشاء الفيديو"}), 500

        except subprocess.CalledProcessError as e:
            print(f"  ❌ خطأ في ffmpeg: {e}")
            print(f"  📄 stdout: {e.stdout}")
            print(f"  📄 stderr: {e.stderr}")
            return jsonify({"error": f"خطأ في إنشاء الفيديو: {e.stderr}"}), 500

        # حذف الملفات المؤقتة بعد 10 ثواني
        remove_file_later(image_path)
        remove_file_later(final_audio_path)
        remove_file_later(output_path)

        return send_file(output_path, as_attachment=True)

    except Exception as e:
        return jsonify({"error": f"حدث خطأ: {str(e)}"}), 500

@app.route('/upload_custom_image', methods=['POST'])
def upload_custom_image():
    """رفع صورة مخصصة للاستخدام في الفيديو"""
    print("🖼️ تم استلام طلب رفع صورة مخصصة")

    if 'image' not in request.files:
        return jsonify({"error": "لم يتم رفع صورة"}), 400

    image_file = request.files['image']

    if image_file.filename == '':
        return jsonify({"error": "لم يتم اختيار صورة"}), 400

    # التحقق من نوع الملف
    allowed_extensions = {'png', 'jpg', 'jpeg', 'gif', 'bmp'}
    file_extension = image_file.filename.rsplit('.', 1)[1].lower() if '.' in image_file.filename else ''

    if file_extension not in allowed_extensions:
        return jsonify({"error": "نوع الملف غير مدعوم. يرجى رفع صورة (PNG, JPG, JPEG, GIF, BMP)"}), 400

    try:
        # إنشاء اسم ملف فريد
        filename = f"{uuid.uuid4().hex}_custom_image.{file_extension}"
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)

        # حفظ الصورة
        image_file.save(filepath)

        # التحقق من حجم الصورة وتحسينها إذا لزم الأمر
        from PIL import Image
        with Image.open(filepath) as img:
            # تحويل إلى RGB إذا كانت RGBA
            if img.mode == 'RGBA':
                img = img.convert('RGB')

            # تحديد الحد الأقصى للأبعاد
            max_width, max_height = 1920, 1080

            # تغيير الحجم إذا كانت الصورة كبيرة جداً
            if img.width > max_width or img.height > max_height:
                img.thumbnail((max_width, max_height), Image.Resampling.LANCZOS)
                print(f"  📏 تم تغيير حجم الصورة إلى: {img.width}x{img.height}")

            # حفظ الصورة المحسنة
            img.save(filepath, quality=90, optimize=True)

        file_size = os.path.getsize(filepath)
        print(f"  ✅ تم رفع الصورة بنجاح: {filepath}")
        print(f"  📊 حجم الملف: {file_size} bytes")
        print(f"  📏 أبعاد الصورة: {img.width}x{img.height}")

        # حذف الصورة بعد ساعة واحدة
        remove_file_later(filepath, delay=3600)

        return jsonify({
            "success": True,
            "image_path": filepath,
            "filename": filename,
            "size": file_size,
            "dimensions": {"width": img.width, "height": img.height}
        })

    except Exception as e:
        print(f"❌ خطأ في رفع الصورة: {e}")
        return jsonify({"error": f"خطأ في معالجة الصورة: {str(e)}"}), 500

@app.route('/create_video', methods=['POST'])
def create_video():
    print("🎬 تم استلام طلب للوظيفة القديمة create_video()")
    print(f"📄 Content-Type: {request.content_type}")
    print(f"📁 Files: {list(request.files.keys())}")
    print(f"📨 JSON data: {request.get_json()}")

    if 'image' not in request.files or 'audio' not in request.files:
        print("❌ لا توجد ملفات مرفوعة، إرجاع خطأ 400")
        return "يرجى رفع صورة وملف صوتي معاً.", 400

    image = request.files['image']
    audio = request.files['audio']

    if image.filename == '' or audio.filename == '':
        return "يرجى رفع صورة وملف صوتي معاً.", 400

    # أسماء ملفات فريدة لتجنب تكرار
    image_filename = f"{uuid.uuid4().hex}_{image.filename}"
    audio_filename = f"{uuid.uuid4().hex}_{audio.filename}"
    image_path = os.path.join(app.config['UPLOAD_FOLDER'], image_filename)
    audio_path = os.path.join(app.config['UPLOAD_FOLDER'], audio_filename)
    output_filename = f"{uuid.uuid4().hex}_output.mp4"
    output_path = None # Initialize output_path to None

    image.save(image_path)
    audio.save(audio_path)

    duration = get_audio_duration(audio_path)
    if duration is None:
        return "تعذر استخراج مدة ملف الصوت.", 500

    try:
        output_path = os.path.join(app.config['UPLOAD_FOLDER'], output_filename)
        subprocess.run([
            'ffmpeg',
            '-y',
            '-loop', '1',
            '-i', image_path,
            '-i', audio_path,
            '-c:v', 'libx264',
            '-tune', 'stillimage',
            '-preset', 'ultrafast',
            '-c:a', 'aac',
            '-b:a', '192k',
            '-pix_fmt', 'yuv420p',
            '-t', str(duration),            # تعيين مدة الفيديو مساوية للصوت
            '-movflags', '+faststart',
            output_path
        ], check=True)

        # حذف الملفات بعد 10 ثواني للسماح بالتحميل
        remove_file_later(image_path)
        remove_file_later(audio_path)
        remove_file_later(output_path)

        return send_file(output_path, as_attachment=True)

    except subprocess.CalledProcessError as e:
        # حذف الملفات فورًا عند الخطأ
        if os.path.exists(image_path):
            os.remove(image_path)
        if os.path.exists(audio_path):
            os.remove(audio_path)
        if os.path.exists(output_path):
            os.remove(output_path)
        return f"حدث خطأ أثناء معالجة الفيديو: {e}", 500

@app.before_request
def log_request_info():
    """تسجيل جميع الطلبات الواردة"""
    print(f"📡 طلب وارد: {request.method} {request.path}")
    if request.method == 'POST':
        print(f"📄 Content-Type: {request.content_type}")
        if request.path == '/create_quran_video':
            print("🎯 هذا طلب إنشاء فيديو قرآني!")
            try:
                data = request.get_json()
                print(f"📨 البيانات: {data}")
            except Exception as e:
                print(f"❌ خطأ في قراءة JSON: {e}")

def get_video_dimensions(orientation):
    """تحديد أبعاد الفيديو حسب الاتجاه المختار"""
    dimensions = {
        'landscape': {'width': 1280, 'height': 720, 'name': 'أفقي (HD)'},
        'portrait': {'width': 720, 'height': 1280, 'name': 'عمودي (موبايل)'},
        'square': {'width': 1080, 'height': 1080, 'name': 'مربع (وسائل التواصل)'}
    }

    return dimensions.get(orientation, dimensions['landscape'])

def convert_to_arabic_numbers(number):
    """تحويل الأرقام الإنجليزية إلى الأرقام الهندية"""
    arabic_numbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩']
    return ''.join(arabic_numbers[int(digit)] for digit in str(number))

def ensure_arabic_verse_numbers(text):
    """التأكد من أن أرقام الآيات بالعربية الهندية"""
    import re

    # البحث عن أرقام الآيات في النص (الأرقام بعد ۝)
    pattern = r'۝(\d+)'

    def replace_verse_number(match):
        verse_num = match.group(1)
        arabic_num = convert_to_arabic_numbers(verse_num)
        return f'۝{arabic_num}'

    # استبدال أرقام الآيات
    result = re.sub(pattern, replace_verse_number, text)

    # حذف أي فواصل فارغة (بدون أرقام)
    result = re.sub(r'۝\s*۝', '', result)  # حذف ۝۝ المتتالية
    result = re.sub(r'۝\s*$', '', result)  # حذف ۝ في النهاية بدون رقم
    result = re.sub(r'^\s*۝', '', result)  # حذف ۝ في البداية بدون رقم
    result = re.sub(r'\s+', ' ', result).strip()  # تنظيف المسافات

    return result

def add_verse_numbers_to_text(verses_data):
    """إضافة أرقام الآيات داخل الفاصل إلى النص"""
    import re
    verses_with_numbers = []
    for index, verse in enumerate(verses_data):
        verse_text = verse.get('text_uthmani', '')

        # تنظيف شامل للنص من أي فواصل موجودة مسبقاً
        clean_text = verse_text.strip()

        # حذف جميع أنواع الفواصل والرموز المشابهة
        clean_text = re.sub(r'[۝﴾﴿]', '', clean_text)  # حذف جميع أنواع الفواصل
        clean_text = re.sub(r'[\u06DD\u06DE\u06DF]', '', clean_text)  # حذف رموز الآيات العربية
        clean_text = re.sub(r'\s+', ' ', clean_text).strip()  # توحيد المسافات

        # إضافة رقم الآية الحالية داخل الفاصل (بدون مسافات)
        current_verse_number = verse.get('verse_number', index + 1)
        arabic_number = convert_to_arabic_numbers(current_verse_number)

        # إنشاء رقم الآية مع فاصل واحد فقط في البداية
        verse_number_symbol = f'۝{arabic_number}'

        # إضافة النص مع رقم الآية (بدون مسافة بين النص والرقم)
        verse_with_number = clean_text + verse_number_symbol
        verses_with_numbers.append(verse_with_number)

    final_text = ' '.join(verses_with_numbers)

    # تنظيف نهائي لحذف أي فواصل فارغة متبقية
    final_text = re.sub(r'۝\s*۝', '', final_text)  # حذف ۝۝ المتتالية
    final_text = re.sub(r'۝\s*$', '', final_text)  # حذف ۝ في النهاية بدون رقم
    final_text = re.sub(r'^\s*۝', '', final_text)  # حذف ۝ في البداية بدون رقم
    final_text = re.sub(r'\s+', ' ', final_text).strip()  # تنظيف المسافات

    return final_text

def process_custom_image_for_video(custom_image_path, verses_text, target_width=None, target_height=None, text_settings=None):
    """معالجة الصورة المخصصة لضمان التوافق مع ffmpeg وإضافة النص القرآني"""
    try:

        # فتح الصورة المخصصة
        with Image.open(custom_image_path) as custom_img:
            # تحويل إلى RGB إذا لزم الأمر
            if custom_img.mode != 'RGB':
                custom_img = custom_img.convert('RGB')

            # الحصول على الأبعاد الحالية
            original_width, original_height = custom_img.size


            # استخدام الأبعاد المستهدفة إذا تم تمريرها، وإلا استخدم الأبعاد الأصلية
            if target_width and target_height:
                new_width = target_width
                new_height = target_height

            else:
                # تحديد أبعاد جديدة قابلة للقسمة على 2
                new_width = original_width if original_width % 2 == 0 else original_width + 1
                new_height = original_height if original_height % 2 == 0 else original_height + 1
                print(f"  📏 الأبعاد المصححة: {new_width}x{new_height}")

            # إنشاء صورة جديدة بالأبعاد المصححة
            processed_img = Image.new('RGB', (new_width, new_height), (0, 0, 0))

            # تغيير حجم الصورة الأصلية لتناسب الأبعاد الجديدة مع الحفاظ على النسبة
            if target_width and target_height:
                # حساب النسبة للحفاظ على شكل الصورة
                ratio = min(new_width / original_width, new_height / original_height)
                resized_width = int(original_width * ratio)
                resized_height = int(original_height * ratio)

                # تغيير حجم الصورة
                custom_img = custom_img.resize((resized_width, resized_height), Image.Resampling.LANCZOS)

                # لصق الصورة في المنتصف
                paste_x = (new_width - resized_width) // 2
                paste_y = (new_height - resized_height) // 2
                processed_img.paste(custom_img, (paste_x, paste_y))


            else:
                # لصق الصورة الأصلية في المنتصف (للحالات القديمة)
                paste_x = (new_width - original_width) // 2
                paste_y = (new_height - original_height) // 2
                processed_img.paste(custom_img, (paste_x, paste_y))

            # إضافة النص القرآني على الصورة
            draw = ImageDraw.Draw(processed_img)

            # تحميل خط عربي واستخدام إعدادات النص المخصصة
            try:
                # استخدام إعدادات النص المخصصة إذا تم تمريرها
                if text_settings:
                    font_size = int(text_settings.get('fontSize', min(new_width, new_height) // 20))
                    text_color = text_settings.get('textColor', '#ffffff')
                    text_position = text_settings.get('textPosition', 'bottom')
                    text_padding = int(text_settings.get('textPadding', 40))
                else:
                    font_size = min(new_width, new_height) // 20  # حجم خط متناسب مع حجم الصورة
                    text_color = '#ffffff'  # أبيض افتراضي
                    text_position = 'bottom'
                    text_padding = 40

                # تحديد مسارات الخطوط
                font_paths = [
                    "fonts/Amiri-Regular.ttf",               # أميري محلي
                    "fonts/amiri.ttf",                       # أميري محلي - اسم بديل
                    "C:/Windows/Fonts/Amiri-Regular.ttf",     # أميري نظام
                    "C:/Windows/Fonts/amiri.ttf",             # أميري نظام - اسم بديل
                ]

                font = None
                for font_path in font_paths:
                    try:
                        font = ImageFont.truetype(font_path, font_size)
                        break
                    except Exception as e:
                        continue

                if font is None:
                    font = ImageFont.load_default()
            except:
                font = ImageFont.load_default()
                text_color = '#ffffff'

            # تحويل الأرقام الإنجليزية إلى العربية الهندية قبل المعالجة
            arabic_numbers_text = ensure_arabic_verse_numbers(verses_text)

            # معالجة النص العربي
            reshaped_text = arabic_reshaper.reshape(arabic_numbers_text)
            bidi_text = get_display(reshaped_text)

            # تقسيم النص إلى أسطر
            words = bidi_text.split(' ')
            lines = []
            current_line = ""
            max_width = new_width - 80  # هامش 40 بكسل من كل جانب

            for word in words:
                test_line = current_line + " " + word if current_line else word
                bbox = draw.textbbox((0, 0), test_line, font=font)
                if bbox[2] - bbox[0] < max_width:
                    current_line = test_line
                else:
                    if current_line:
                        lines.append(current_line)
                    current_line = word

            if current_line:
                lines.append(current_line)

            # عكس ترتيب الأسطر للنص العربي (السطر الأول يجب أن يكون في الأعلى)
            lines.reverse()
            print(f"📝 تم تقسيم النص في الصورة المخصصة إلى {len(lines)} سطر وعكس الترتيب للعربية")

            # حساب موقع النص حسب الإعداد المختار
            line_height = font_size + 10
            total_text_height = len(lines) * line_height

            # حساب موقع النص حسب الإعداد المختار
            if text_position == 'top':
                start_y = text_padding
            elif text_position == 'center':
                start_y = (new_height - total_text_height) // 2
            elif text_position == 'bottom-center':
                start_y = new_height - total_text_height - (text_padding * 2)
            else:  # 'bottom' (افتراضي)
                start_y = new_height - total_text_height - text_padding

            print(f"📍 موقع النص في الصورة المخصصة: start_y = {start_y} (موقع: {text_position})")

            # رسم خلفية شبه شفافة للنص
            overlay = Image.new('RGBA', (new_width, new_height), (0, 0, 0, 0))
            overlay_draw = ImageDraw.Draw(overlay)

            # رسم مستطيل شبه شفاف خلف النص
            text_bg_y1 = start_y - 20
            text_bg_y2 = start_y + total_text_height + 20
            overlay_draw.rectangle([(0, text_bg_y1), (new_width, text_bg_y2)],
                                 fill=(0, 0, 0, 180))  # أسود شبه شفاف

            # دمج الخلفية الشبه شفافة مع الصورة
            processed_img = Image.alpha_composite(processed_img.convert('RGBA'), overlay).convert('RGB')
            draw = ImageDraw.Draw(processed_img)

            # رسم النص
            for i, line in enumerate(lines):
                bbox = draw.textbbox((0, 0), line, font=font)
                text_width = bbox[2] - bbox[0]
                x = (new_width - text_width) // 2
                y = start_y + i * line_height

                # إضافة ظل للنص
                draw.text((x+2, y+2), line, font=font, fill=(0, 0, 0))
                # استخدام اللون المخصص للنص
                draw.text((x, y), line, font=font, fill=text_color)

            # حفظ الصورة المعالجة
            filename = f"{uuid.uuid4().hex}_processed_custom_image.png"
            filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
            processed_img.save(filepath, quality=95)

            print(f"  ✅ تم معالجة الصورة بنجاح: {filepath}")
            print(f"  📊 الأبعاد النهائية: {new_width}x{new_height}")

            # حذف الصورة بعد ساعة واحدة
            remove_file_later(filepath)

            return filepath

    except Exception as e:
        print(f"❌ خطأ في معالجة الصورة المخصصة: {e}")
        # في حالة الخطأ، إنشاء صورة افتراضية
        return create_verse_image(verses_text, target_width, target_height, text_settings)

if __name__ == '__main__':
    print("🚀 بدء تشغيل خادم Flask...")
    print("📍 الخادم متاح على: http://localhost:5000")
    print("🔧 وضع التطوير: مفعل")
    print("📝 تم تحديث إعدادات الخطوط لمطابقة الآيات المختارة والمعاينة")
    print("🎯 خط Amiri متاح في: fonts/Amiri-Regular.ttf")
    print("=" * 50)
    app.run(debug=True, host='0.0.0.0', port=5000)
