# 🕌 مولد فيديوهات القرآن الكريم

موقع Flask متطور لإنشاء فيديوهات "ريلز قرآن كريم" يدمج الآيات القرآنية مع التلاوة الصوتية لإنتاج فيديوهات جذابة للمشاركة على وسائل التواصل الاجتماعي.

## ✨ المميزات

### 📖 إنشاء من القرآن الكريم
- **اختيار السور**: قائمة منسدلة تحتوي على جميع سور القرآن الكريم
- **اختيار الآيات**: إمكانية اختيار آيات متعددة من السورة المختارة
- **اختيار القارئ**: قائمة بأشهر القراء مع تلاواتهم عالية الجودة
- **معاينة الآيات**: عرض الآيات المختارة بالخط العثماني قبل الإنشاء
- **توليد الصورة التلقائي**: إنشاء صورة تحتوي على الآيات تلقائياً

### 📁 رفع ملفات مخصصة
- إمكانية رفع صورة وملف صوتي مخصص
- دعم تنسيقات متعددة للصور والصوت
- معالجة متقدمة للملفات

### 🎬 إنتاج الفيديو
- دمج الصوت مع الصورة باستخدام ffmpeg
- جودة عالية للفيديو النهائي
- تحسين للمشاركة على وسائل التواصل الاجتماعي

## 🛠️ المتطلبات

- **Python 3.7+**
- **ffmpeg** (مثبت في النظام)
- **اتصال بالإنترنت** (لجلب بيانات القرآن والتلاوات)

## 📦 التثبيت

### 1. تثبيت المتطلبات

```bash
pip install -r requirements.txt
```

### 2. تثبيت ffmpeg

#### Windows:
- تحميل من [https://ffmpeg.org/download.html](https://ffmpeg.org/download.html)
- إضافة مجلد `bin` إلى متغير البيئة PATH

#### macOS:
```bash
brew install ffmpeg
```

#### Linux:
```bash
# Ubuntu/Debian
sudo apt-get install ffmpeg

# CentOS/RHEL
sudo yum install ffmpeg
```

## 🚀 الاستخدام

### 1. تشغيل الخادم

```bash
cd backend
python app.py
```

### 2. فتح الواجهة

افتح ملف `frontend/index.html` في المتصفح أو استخدم خادم محلي:

```bash
cd frontend
python -m http.server 8000
```

ثم اذهب إلى: `http://localhost:8000`

### 3. إنشاء فيديو قرآني

1. **اختر السورة** من القائمة المنسدلة
2. **اختر الآيات** المطلوبة (يمكن اختيار عدة آيات)
3. **اختر القارئ** المفضل
4. **اختياري**: رفع صورة مخصصة
5. **اضغط "توليد الفيديو"**
6. **انتظر** حتى اكتمال المعالجة
7. **حمل الفيديو** النهائي

## 🔧 التكوين

### APIs المستخدمة

- **Quran.com API**: لجلب بيانات السور والآيات
- **Verses Audio API**: لتحميل التلاوات الصوتية

### إعدادات الخادم

يمكن تعديل الإعدادات في `backend/app.py`:

```python
# URLs للـ APIs
QURAN_API_BASE = "https://api.quran.com/api/v4"
AUDIO_URL = "https://verses.quran.com"
```

## 📁 هيكل المشروع

```
├── backend/
│   ├── app.py              # الخادم الرئيسي
│   └── uploads/            # مجلد الملفات المؤقتة
├── frontend/
│   ├── index.html          # الواجهة الرئيسية
│   ├── style.css           # التصميم
│   └── script.js           # الوظائف التفاعلية
├── requirements.txt        # متطلبات Python
└── README.md              # هذا الملف
```

## 🎨 المميزات التقنية

### Backend (Flask)
- **APIs متعددة**: endpoints لجلب السور والآيات والقراء
- **تنزيل تلقائي**: تحميل التلاوات من الإنترنت
- **معالجة الصور**: إنشاء صور تحتوي على النصوص العربية
- **دمج الصوت**: دمج عدة ملفات صوتية إذا لزم الأمر
- **تنظيف تلقائي**: حذف الملفات المؤقتة تلقائياً

### Frontend
- **واجهة عربية**: تصميم يدعم اللغة العربية بالكامل
- **تبويبات**: تنظيم الوظائف في تبويبات منفصلة
- **معاينة مباشرة**: عرض الآيات المختارة قبل الإنشاء
- **تفاعلية**: واجهة سهلة الاستخدام مع تحديث مباشر

## 🔍 استكشاف الأخطاء

### مشاكل شائعة:

1. **خطأ في ffmpeg**:
   - تأكد من تثبيت ffmpeg وإضافته للـ PATH

2. **فشل في تحميل البيانات**:
   - تحقق من اتصال الإنترنت
   - تأكد من عمل APIs الخارجية

3. **مشاكل في الخطوط العربية**:
   - تأكد من تثبيت مكتبات `arabic-reshaper` و `python-bidi`

## 📝 ملاحظات

- الخادم يعمل على المنفذ 5000 افتراضياً
- الملفات المؤقتة تُحذف تلقائياً بعد 10 ثواني
- يدعم تنسيقات الصور: JPEG, PNG
- يدعم تنسيقات الصوت: MP3, WAV

## 🤝 المساهمة

نرحب بالمساهمات! يمكنك:
- إضافة مميزات جديدة
- تحسين التصميم
- إصلاح الأخطاء
- تحسين الوثائق

## 📄 الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام الشخصي والتعليمي.
